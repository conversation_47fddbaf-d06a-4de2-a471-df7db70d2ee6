import { ApiListResponse, ApiResponse } from "../../types/api";
import { OrderItem, OrderFormData, OrderFileItem, OrderStatusLog } from "../../types/order";
import api from "../api";
import { getContractsSimpleList } from "../contract";

export interface OrdersParams {
  page?: number;
  pageSize?: number;
  contract_legal_num?: string;
  customer_num?: string;
  total_num?: string;
  order_type?: string;
  pay_type?: string;
  service_type?: string;
  service_status?: string;
  bill_status?: string;
  create_user?: string;
  business_product_type?: string;
  [key: string]: any;
}

export const getOrders = async (
  params: OrdersParams
): Promise<ApiListResponse<OrderItem[]>> => {
  const response = await api.get("/orders-info", { params });
  return response.data;
};

export const createOrder = async (data: OrderFormData): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if ((data.product_files && data.product_files.length > 0) || 
      (data.finished_files && data.finished_files.length > 0)) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'product_files' && key !== 'finished_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加产品文件
    if (data.product_files) {
      data.product_files.forEach((file, index) => {
        formData.append(`product_files[${index}]`, file);
      });
    }

    // 添加完工文件
    if (data.finished_files) {
      data.finished_files.forEach((file, index) => {
        formData.append(`finished_files[${index}]`, file);
      });
    }

    const response = await api.post("/orders-info", formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.post("/orders-info", data);
    return response.data;
  }
};

export const updateOrder = async (
  id: number,
  data: Partial<OrderFormData>
): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if ((data.product_files && data.product_files.length > 0) || 
      (data.finished_files && data.finished_files.length > 0)) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'product_files' && key !== 'finished_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加产品文件
    if (data.product_files) {
      data.product_files.forEach((file, index) => {
        formData.append(`product_files[${index}]`, file);
      });
    }

    // 添加完工文件
    if (data.finished_files) {
      data.finished_files.forEach((file, index) => {
        formData.append(`finished_files[${index}]`, file);
      });
    }

    const response = await api.put(`/orders-info/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.put(`/orders-info/${id}`, data);
    return response.data;
  }
};

// 变更订单
export const changeOrder = async (
  id: number,
  data: OrderFormData
): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if ((data.product_files && data.product_files.length > 0) || 
      (data.finished_files && data.finished_files.length > 0)) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'product_files' && key !== 'finished_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加产品文件
    if (data.product_files) {
      data.product_files.forEach((file, index) => {
        formData.append(`product_files[${index}]`, file);
      });
    }

    // 添加完工文件
    if (data.finished_files) {
      data.finished_files.forEach((file, index) => {
        formData.append(`finished_files[${index}]`, file);
      });
    }

    const response = await api.post(`/orders-info/${id}/change-order`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.post(`/orders-info/${id}/change-order`, data);
    return response.data;
  }
};

// 新建子订单
export const createSubOrder = async (
  id: number,
  data: OrderFormData
): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if ((data.product_files && data.product_files.length > 0) || 
      (data.finished_files && data.finished_files.length > 0)) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'product_files' && key !== 'finished_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加产品文件
    if (data.product_files) {
      data.product_files.forEach((file, index) => {
        formData.append(`product_files[${index}]`, file);
      });
    }

    // 添加完工文件
    if (data.finished_files) {
      data.finished_files.forEach((file, index) => {
        formData.append(`finished_files[${index}]`, file);
      });
    }

    const response = await api.post(`/orders-info/${id}/new-sub-order`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.post(`/orders-info/${id}/new-sub-order`, data);
    return response.data;
  }
};

// 续约订单
export const createRenewalOrder = async (
  id: number,
  data: OrderFormData
): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if ((data.product_files && data.product_files.length > 0) || 
      (data.finished_files && data.finished_files.length > 0)) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'product_files' && key !== 'finished_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加产品文件
    if (data.product_files) {
      data.product_files.forEach((file, index) => {
        formData.append(`product_files[${index}]`, file);
      });
    }

    // 添加完工文件
    if (data.finished_files) {
      data.finished_files.forEach((file, index) => {
        formData.append(`finished_files[${index}]`, file);
      });
    }

    const response = await api.post(`/orders-info/${id}/renewal-order`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.post(`/orders-info/${id}/renewal-order`, data);
    return response.data;
  }
};

export const getOrderDetail = async (
  id: number
): Promise<ApiResponse<OrderItem>> => {
  const response = await api.get(`/orders-info/${id}`);
  return response.data;
};

export interface ChangeServiceStatusData {
  service_status: string;
  finished_remark?: string;
  new_build_start_time?: string;
  order_remark?: string;
  job_status?: string;
  product_scheme?: string;
  remove_required_finished_date?: string;
  remove_build_start_time?: string;
}

export const changeServiceStatus = async (
  orderId: number,
  data: ChangeServiceStatusData
) => {
  return api.post(`/orders-info/${orderId}/change-service-status`, data);
};

// 计费状态变更参数接口
export interface ChangeBillingStatusData {
  bill_status: string;
  account_seq?: string;
  reality_bill_start_date?: string;
  order_start_year?: string;
  order_remark?: string;
  finished_remark?: string;
  reality_bill_end_date?: string;
}

// 变更计费状态
export const changeBillingStatus = (
  orderId: number,
  data: ChangeBillingStatusData
) => {
  return api.post(`/orders-info/${orderId}/change-billing-status`, data);
};

// 导出客户简单列表方法供合同页面使用
export { getContractsSimpleList };

// 获取订单文件列表
export const getOrderFiles = async (
  orderId: number
): Promise<ApiResponse<OrderFileItem[]>> => {
  const response = await api.get(`/orders/${orderId}/order-files`);
  return response.data;
};

// 预览订单文件
export const previewOrderFile = async (
  orderId: number,
  fileId: number
): Promise<{ blob: Blob; filename: string }> => {
  const response = await api.get(
    `/orders/${orderId}/order-files/${fileId}/attachment-preview`,
    {
      responseType: 'blob',
    }
  );

  // 从响应头中获取文件名
  const contentDisposition = response.headers['content-disposition'];
  let filename = 'order-file';

  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
    if (filenameMatch && filenameMatch[1]) {
      try {
        filename = decodeURIComponent(filenameMatch[1]);
      } catch (error) {
        console.error('Error decoding filename:', error);
      }
    }
  }

  return {
    blob: response.data,
    filename: filename,
  };
};

// 获取订单状态变更日志
export const getOrderStatusLogs = async (
  orderId: number,
  statusType: 'service' | 'bill'
): Promise<ApiResponse<OrderStatusLog[]>> => {
  const response = await api.get(`/orders/${orderId}/order-status-log`, {
    params: { status_type: statusType }
  });
  return response.data;
};
