import { ApiListResponse, ApiResponse } from "../types/api";
import {
  ContractFormData,
  ContractItem,
  ContractSimpleInfo,
  ContractFileItem,
} from "../types/contract";
import { getCustomersSimpleList } from "./customer";
import api from "./api";

export interface ContractsParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
  main_customer_num?: string;
  [key: string]: any;
}

export const getContracts = async (
  params: ContractsParams
): Promise<ApiListResponse<ContractItem[]>> => {
  const response = await api.get("/customers/contracts", { params });
  return response.data;
};

export const createContract = async (data: ContractFormData): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if (data.contract_files && data.contract_files.length > 0) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'contract_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加文件
    data.contract_files.forEach((file, index) => {
      formData.append(`contract_files[${index}]`, file);
    });

    const response = await api.post("/customers/contracts", formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.post("/customers/contracts", data);
    return response.data;
  }
};

export const updateContract = async (
  id: string,
  data: Partial<ContractFormData>
): Promise<any> => {
  // 检查是否包含文件，如果包含则使用FormData
  if (data.contract_files && data.contract_files.length > 0) {
    const formData = new FormData();

    // 添加基本字段
    Object.keys(data).forEach(key => {
      if (key !== 'contract_files') {
        const value = (data as any)[key];
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      }
    });

    // 添加文件
    data.contract_files.forEach((file, index) => {
      formData.append(`contract_files[${index}]`, file);
    });

    const response = await api.put(`/customers/contracts/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } else {
    const response = await api.put(`/customers/contracts/${id}`, data);
    return response.data;
  }
};

export const getContractDetail = async (
  id: string
): Promise<ApiResponse<ContractItem>> => {
  const response = await api.get<ApiResponse<ContractItem>>(
    `/customers/contracts/${id}`
  );
  return response.data;
};

// 导出客户简单列表方法供合同页面使用
export { getCustomersSimpleList };

// 获取合同信息简单列表（用于下拉框）
export const getContractsSimpleList = async (): Promise<
  ApiResponse<ContractSimpleInfo[]>
> => {
  const response = await api.get("/customers/contracts/simple-list");
  return response.data;
};

// 获取合同文件列表
export const getContractFiles = async (
  contractId: number
): Promise<ApiResponse<ContractFileItem[]>> => {
  const response = await api.get(`/contracts/${contractId}/contract-files`);
  return response.data;
};

// 预览合同文件
export const previewContractFile = async (
  contractId: number,
  fileId: number
): Promise<{ blob: Blob; filename: string }> => {
  const response = await api.get(
    `/contracts/${contractId}/contract-files/${fileId}/attachment-preview`,
    {
      responseType: 'blob',
    }
  );

  // 从响应头中获取文件名
  const contentDisposition = response.headers['content-disposition'];
  let filename = 'contract-file';

  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
    if (filenameMatch && filenameMatch[1]) {
      try {
        filename = decodeURIComponent(filenameMatch[1]);
      } catch (error) {
        console.error('Error decoding filename:', error);
      }
    }
  }

  return {
    blob: response.data,
    filename: filename,
  };
};
