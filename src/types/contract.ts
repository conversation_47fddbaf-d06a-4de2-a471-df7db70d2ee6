export interface ContractItem {
  id: number;
  contract_num: string;
  contract_title: string;
  business: string;
  alias_customer_num: string;
  contract_type: string;
  sale_name: string;
  frame_contract_num: string;
  frame_legal_num: string;
  frame_oa_num: string;
  main_customer_num: string;
  customer_name: string;
  sign_contract_entity: string;
  contract_legal_num: string;
  contract_oa_num: string;
  other_party_num: string;
  remark: string;
  contract_start_date: Date;
  is_auto_delay: string;
  auto_delay_num: number;
  contract_month_amount: number;
  contract_summary: string;
  contract_term: number;
  contract_object: string;
  contract_end_date: Date;
  append_contract_way: string;
  append_contract_explain: string;
  group_approve_state: string;
  create_user: string;
  state: string;
  created_at: string;
  contact_person: string;
  contact_address: string;
  contact_telephone: string;
  contact_email: string;
  bank_account_number: string;
  bank_account_name: string;
  bank_name: string;
  applicant_name: string;
}

// 客户新建/编辑时的表单数据信息
export interface ContractFormData
  extends Omit<
    ContractItem,
    | "id"
    | "created_at"
    | "create_user"
    | "group_approve_state"
    | "customer_name"
  > {
  contract_files?: File[];
}

// 合同文件信息
export interface ContractFileItem {
  id: number;
  file_name: string;
  status: "unsigned" | "signed";
}

// 客户简单信息（下拉列表用）
export interface ContractSimpleInfo {
  id: number;
  contract_num: string;
  contract_title: string;
  main_customer_num: string;
}
