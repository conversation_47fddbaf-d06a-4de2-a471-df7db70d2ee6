// 调账记录相关类型定义

export interface AdjustDetailItem {
  id: number;
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  adjust_month: number;
  adjust_amount: number;
  adjust_tax: number;
  adjust_reason_class: string;
  adjust_reason: string;
  state: number;
  adjust_charge_detail_id: number | null;
  adjust_type: number | null;
  create_user: string | null;
  created_at: string;
  approve_user: string | null;
}

export interface AdjustDetailParams {
  page?: number;
  pageSize?: number;
  adjust_month?: string;
  adjust_type?: number;
  charge_month?: string;
  order_no?: string;
  [key: string]: any;
}

// 权责调账相关类型定义
export interface ChargeAdjustItem {
  id: number;
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  fee_amount: number;
  income_type: string;
  tax_type: string;
  customer_name: string;
  tax: number; // 添加税率字段
}

export interface ChargeAdjustParams {
  page?: number;
  pageSize?: number;
  charge_month?: string;
  sub_order_no?: string;
  customer_num?: string;
  [key: string]: any;
}

// 调账请求数据类型
export interface AdjustAccountRequest {
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  adjust_month: number;
  adjust_amount: number;
  adjust_tax: number;
  adjust_reason_class: string;
  adjust_reason: string;
  adjust_charge_detail_id: number;
}

// 收入调账相关类型定义
export interface IncomeAdjustItem {
  id: number;
  order_num: string;
  total_num: string;
  bill_status: string;
  income_type: string;
  tax_type: string | null;
  tax_rate: number | null;
  main_customer_num: string;
}

export interface IncomeAdjustParams {
  page?: number;
  pageSize?: number;
  order_num?: string;
  total_num?: string;
  [key: string]: any;
}

// 无权责调账请求数据类型
export interface IncomeAdjustAccountRequest {
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  adjust_month: number;
  adjust_amount: number;
  adjust_tax: number;
  adjust_reason_class: string;
  adjust_reason: string;
}
