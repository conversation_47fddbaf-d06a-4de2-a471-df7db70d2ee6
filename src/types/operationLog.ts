// 操作记录相关类型定义

// 操作类型枚举
export type OperationType = "create" | "update";

// 模块名称枚举
export type ModuleName = "customer" | "contact" | "contract" | "order" | "user" | "role";

// 字段变更信息接口
export interface FieldChange {
  field_name: string;
  old_value: string;
  new_value: string;
}

// 操作记录项接口
export interface OperationLogItem {
  id: number;
  operator: string;
  operation_type: OperationType;
  table_name: ModuleName;
  table_id: number;
  operation_content: string | FieldChange[];
  created_at: string; // 时间戳字符串
}

// 操作记录搜索参数接口
export interface OperationLogSearchParams {
  operation_type?: OperationType;
  table_name?: ModuleName;
  page?: number;
  pageSize?: number;
}

// 操作类型标签映射
export const OperationTypeMap: Record<OperationType, string> = {
  create: "新建",
  update: "编辑",
};

// 模块名称标签映射
export const ModuleNameMap: Record<ModuleName, string> = {
  customer: "客户",
  contact: "客户联系人",
  contract: "合同",
  order: "订单",
  user: "用户",
  role: "角色",
};

// 操作类型图标映射
export const OperationTypeIconMap: Record<OperationType, string> = {
  create: "pi pi-plus-circle",
  update: "pi pi-pencil",
};

// 操作类型颜色映射
export const OperationTypeColorMap: Record<OperationType, string> = {
  create: "success",
  update: "info",
};

// 模块图标映射
export const ModuleIconMap: Record<ModuleName, string> = {
  customer: "pi pi-users",
  contact: "pi pi-book",
  contract: "pi pi-file",
  order: "pi pi-shopping-cart",
  user: "pi pi-user",
  role: "pi pi-lock",
};