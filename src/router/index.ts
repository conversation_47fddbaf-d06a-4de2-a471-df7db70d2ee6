import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      redirect: "/home",
    },
    {
      path: "/login",
      component: () => import("../views/Login.vue"),
    },
    {
      path: "/forgot-password",
      component: () => import("../views/ForgotPassword.vue"),
    },
    {
      path: "/",
      component: () => import("../views/Dashboard.vue"),
      children: [
        // 首页路由
        {
          path: "/home",
          component: () => import("../views/Home.vue"),
          meta: {
            title: "首页",
            requiresAuth: true,
          },
        },
        // 用户管理路由
        {
          path: "user-management/roles",
          component: () => import("../views/user/RoleList.vue"),
          meta: {
            title: "角色",
            requiresAuth: true,
          },
        },
        {
          path: "user-management/departments",
          component: () => import("../views/user/DepartmentList.vue"),
          meta: {
            title: "部门管理",
            requiresAuth: true,
          },
        },
        {
          path: "user-management/menus",
          component: () => import("../views/menu/MenuList.vue"),
          meta: {
            title: "菜单信息",
            requiresAuth: true,
          },
        },
        // 客户管理路由
        {
          path: "customer-management/customers-info",
          component: () => import("../views/customer/CustomerList.vue"),
          meta: {
            title: "客户信息",
            requiresAuth: true,
          },
        },
        // 客户审批路由
        {
          path: "customer-management/customer-approval",
          component: () => import("../views/customer/CustomerApproval.vue"),
          meta: {
            title: "客户审批",
            requiresAuth: true,
          },
        },
        // 分账序号路由
        {
          path: "/customer-management/account-seq",
          name: "AccountSeq",
          component: () => import("../views/customer/AccountSeqList.vue"),
          meta: {
            title: "分账序号",
            requiresAuth: true,
          },
        },
        // 合同管理路由
        {
          path: "contract-management/contracts-info",
          component: () => import("../views/contract/ContractList.vue"),
          meta: {
            title: "客户信息",
            requiresAuth: true,
          },
        },
        // 发票开具路由
        {
          path: "invoice-management/invoice",
          name: "InvoiceBilling",
          component: () => import("../views/invoice/InvoiceBilling.vue"),
          meta: {
            title: "发票开具",
            requiresAuth: true,
          },
        },
        // 订单管理路由
        {
          path: "order-management/orders-info",
          component: () => import("../views/income/OrderList.vue"),
          meta: {
            title: "订单信息",
            requiresAuth: true,
          },
        },
        // 费用模板路由
        {
          path: "order-management/fee-templates",
          component: () => import("../views/income/FeeTemplateList.vue"),
          meta: {
            title: "费用模板",
            requiresAuth: true,
          },
        },
        // 费用套餐路由
        {
          path: "order-management/fee-packages",
          component: () => import("../views/income/FeePackageList.vue"),
          meta: {
            title: "费用套餐",
            requiresAuth: true,
          },
        },
        // 收入权责路由
        {
          path: "account-management/charge-details",
          component: () => import("../views/account/ChargeDetailList.vue"),
          meta: {
            title: "收入权责",
            requiresAuth: true,
          },
        },
        // 收入调账路由
        {
          path: "account-management/adjust",
          component: () => import("../views/account/Adjust.vue"),
          meta: {
            title: "收入调账",
            requiresAuth: true,
          },
        },
        // 银行流水路由
        {
          path: "receipt-management/bank-flow",
          component: () => import("../views/receipt/bankFlow.vue"),
          meta: {
            title: "银行流水",
            requiresAuth: true,
          },
        },
      ],
    },
  ],
});

import { checkAuth } from "../services/auth";

router.beforeEach(async (to, _from, next) => {
  try {
    if (to.meta.requiresAuth) {
      const response = await checkAuth();
      if (response.code === 200) {
        if (to.path === "/login" || to.path === "/forgot-password") {
          next("/");
        } else {
          next();
        }
      } else {
        next("/login");
      }
    } else {
      next();
    }
  } catch (error) {
    console.error("Auth check failed:", error);
    next("/login");
  }
});

export default router;
