<script setup lang="ts">
import { useToast } from "primevue/usetoast";
import { onMounted, ref, watch } from "vue";
import { usePermission } from "../../composables/usePermission";
import {
  getCustomersSimpleList,
  createAccountSeq,
} from "../../services/customer";
import type { AccountSeqInfo } from "../../types/customer";
import { formatDateTime } from "../../utils/common";
import type {
  InvoiceInfoItem,
  InvoiceSearchParams,
  InvoiceFormData,
} from "../../types/invoice";
import {
  getAccountSeqInvoiceList,
  getInvoiceDetail,
  createAccountSeqInvoice,
  adjustInvoicePriority,
} from "../../services/invoice";
import {
  InvoiceType,
  CustomerVerifyType,
  CustomerFareOrder,
  IsUnusualNeed,
  IsOpenCharge,
  PostalType,
} from "../../types/invoice";
import {
  getInvoiceTypeSeverity,
  getCustomerVerifyTypeSeverity,
  getTaxSeverity,
} from "../../utils/const";

const accountSeqs = ref<AccountSeqInfo[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

const selectedCustomerNum = ref<string>("");

// 筛选相关
const accountSeqFilter = ref("");
const seqNameFilter = ref("");

// 加载客户简单列表
const customerOptions = ref<
  { label: string; value: string; customer_id: number }[]
>([]);
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    customerOptions.value = response.data.map((item) => ({
      label: `${item.customer_name} (${item.customer_num})`,
      value: item.customer_num,
      customer_id: item.id,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 加载分账序号列表数据
const loadAccountSeqs = async () => {
  try {
    loading.value = true;
    const params: any = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    // 使用选中的客户编号进行筛选
    if (selectedCustomerNum.value) {
      params.customer_num = selectedCustomerNum.value;
    }
    if (accountSeqFilter.value) {
      params.account_seq = accountSeqFilter.value;
    }
    if (seqNameFilter.value) {
      params.seq_name = seqNameFilter.value;
    }

    const response = await fetch(
      "/api/account-seq?" + new URLSearchParams(params)
    );
    const data = await response.json();

    accountSeqs.value = data.data.records;
    totalRecords.value = data.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载分账序号列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadAccountSeqs();
};

// 监听客户编号变化，重新加载分账序号列表
watch(selectedCustomerNum, () => {
  lazyParams.value.page = 1; // 重置到第一页
  loadAccountSeqs();
});

onMounted(async () => {
  await initializeUserInfo();
  loadCustomerOptions();
  loadAccountSeqs();
});

// 表单相关
const accountSeqDrawerVisible = ref(false);
const accountSeqForm = ref({
  customer_id: 0,
  seq_name: "",
  tax: 0,
});

// 税率选项
const taxOptions = [
  { label: "0%", value: 0 },
  { label: "6%", value: 6 },
  { label: "9%", value: 9 },
  { label: "13%", value: 13 },
];

// 字段错误信息
const fieldErrors = ref<{ [key: string]: string }>({});

// 发票相关状态
const invoices = ref<InvoiceInfoItem[]>([]);
const invoiceLoading = ref(false);
const invoiceTotalRecords = ref(0);
const selectedAccountSeq = ref<AccountSeqInfo | null>(null);
const selectedCreateUser = ref("");
const selectedCustomerInvoiceName = ref("");
const invoiceDetailDialog = ref(false);
const selectedInvoice = ref<InvoiceInfoItem | null>(null);

// 发票表单相关状态
const invoiceDrawerVisible = ref(false);
const invoiceForm = ref<InvoiceFormData>({
  id: undefined,
  customer_invoice_name: "",
  customer_deposit_bank: "",
  customer_deposit_bank_sub: "",
  customer_bank_account_name: "",
  customer_bank_account: "",
  customer_tax_number: "",
  customer_invoice_type: InvoiceType.VAT_SPECIAL_TICKET,
  customer_receive_explain: "",
  customer_verify_type: CustomerVerifyType.NO_VERIFY,
  customer_fare_order: CustomerFareOrder.TICKET_FIRST,
  is_unusual_need: IsUnusualNeed.NO,
  unusual_need_explain: "",
  is_open_charge: IsOpenCharge.SUSPENDED,
  postal_type: PostalType.EMAIL,
  postal_address: "",
  email_address: "",
  phone_number: "",
});
const invoiceFieldErrors = ref<{ [key: string]: string }>({});
const isSubmittingInvoice = ref(false);

// 发票分页参数
const invoiceLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 打开新建对话框
const openNew = () => {
  accountSeqForm.value = {
    customer_id: 0,
    seq_name: "",
    tax: 0,
  };
  accountSeqDrawerVisible.value = true;
  fieldErrors.value = {};
};

// 保存分账序号信息
const saveAccountSeq = async () => {
  try {
    // 清空之前的错误信息
    await createAccountSeq(accountSeqForm.value);
    // 创建成功，关闭抽屉并显示成功消息
    accountSeqDrawerVisible.value = false;
    toast.add({
      severity: "success",
      summary: "成功",
      detail: "分账序号创建成功",
      life: 3000,
    });
    loadAccountSeqs();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "分账序号创建失败",
        life: 3000,
      });
    }
  }
};

// 当选中分账序号时加载发票信息
const onAccountSeqSelect = (event: any) => {
  const accountSeq = event.data;
  selectedAccountSeq.value = accountSeq;
  // 重置发票筛选条件
  selectedCreateUser.value = "";
  selectedCustomerInvoiceName.value = "";
  invoiceLazyParams.value.page = 1;
  loadInvoices();
};

// 取消选中分账序号
const onAccountSeqUnselect = () => {
  selectedAccountSeq.value = null;
  invoices.value = [];
  invoiceTotalRecords.value = 0;
};

// 加载发票列表
const loadInvoices = async () => {
  if (!selectedAccountSeq.value) {
    invoices.value = [];
    invoiceTotalRecords.value = 0;
    return;
  }

  try {
    invoiceLoading.value = true;
    const params: InvoiceSearchParams = {
      page: invoiceLazyParams.value.page,
      pageSize: invoiceLazyParams.value.pageSize,
    };

    if (selectedCreateUser.value) {
      params.create_user = selectedCreateUser.value;
    }

    if (selectedCustomerInvoiceName.value) {
      params.customer_invoice_name = selectedCustomerInvoiceName.value;
    }

    const response = await getAccountSeqInvoiceList(
      selectedAccountSeq.value.id,
      params
    );
    if (response.code === 200) {
      invoices.value = response.data.records;
      invoiceTotalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "加载发票列表失败",
        life: 4000,
      });
    }
  } catch (error) {
    console.error("Failed to load invoice list:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票列表失败",
      life: 4000,
    });
  } finally {
    invoiceLoading.value = false;
  }
};

// 处理发票分页事件
const onInvoicePage = (event: { page: number; rows: number }) => {
  invoiceLazyParams.value.page = event.page + 1;
  invoiceLazyParams.value.pageSize = event.rows;
  loadInvoices();
};

// 发票搜索
const handleInvoiceSearch = () => {
  invoiceLazyParams.value.page = 1;
  loadInvoices();
};

// 重置发票筛选条件
const resetInvoiceFilters = () => {
  selectedCreateUser.value = "";
  selectedCustomerInvoiceName.value = "";
  invoiceLazyParams.value.page = 1;
  loadInvoices();
};

// 查看发票详情
const viewInvoice = async (invoice: InvoiceInfoItem) => {
  try {
    const response = await getInvoiceDetail(
      invoice.id!,
      selectedAccountSeq.value!.id
    );
    if (response.code === 200) {
      selectedInvoice.value = response.data;
      invoiceDetailDialog.value = true;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票详情失败",
      life: 3000,
    });
  }
};

// 新建发票信息
const createNewInvoice = () => {
  if (!selectedAccountSeq.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择分账序号",
      life: 3000,
    });
    return;
  }

  invoiceForm.value = {
    id: undefined,
    customer_invoice_name: "",
    customer_deposit_bank: "",
    customer_deposit_bank_sub: "",
    customer_bank_account_name: "",
    customer_bank_account: "",
    customer_tax_number: "",
    customer_invoice_type: InvoiceType.VAT_SPECIAL_TICKET,
    customer_receive_explain: "",
    customer_verify_type: CustomerVerifyType.NO_VERIFY,
    customer_fare_order: CustomerFareOrder.TICKET_FIRST,
    is_unusual_need: IsUnusualNeed.NO,
    unusual_need_explain: "",
    is_open_charge: IsOpenCharge.SUSPENDED,
    postal_type: PostalType.EMAIL,
    postal_address: "",
    email_address: "",
    phone_number: "",
  };
  invoiceFieldErrors.value = {};
  invoiceDrawerVisible.value = true;
};

// 保存发票信息
const saveInvoice = async () => {
  if (!selectedAccountSeq.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择分账序号",
      life: 3000,
    });
    return;
  }

  try {
    isSubmittingInvoice.value = true;
    invoiceFieldErrors.value = {};

    // 必填字段验证
    const requiredFields = [
      { field: "customer_invoice_name", label: "开票名称" },
      { field: "customer_tax_number", label: "税号" },
      { field: "customer_deposit_bank", label: "开户银行" },
      { field: "customer_deposit_bank_sub", label: "开户支行" },
      { field: "customer_bank_account_name", label: "银行账户名" },
      { field: "customer_bank_account", label: "银行账号" },
    ];

    let hasValidationError = false;
    requiredFields.forEach(({ field, label }) => {
      if (!invoiceForm.value[field as keyof typeof invoiceForm.value]) {
        invoiceFieldErrors.value[field] = `${label}不能为空`;
        hasValidationError = true;
      }
    });

    // 根据邮寄方式验证相关字段
    if (
      invoiceForm.value.postal_type === "快递" ||
      invoiceForm.value.postal_type === "邮件且快递"
    ) {
      if (!invoiceForm.value.postal_address) {
        invoiceFieldErrors.value.postal_address = "邮寄地址不能为空";
        hasValidationError = true;
      }
    }

    if (
      invoiceForm.value.postal_type === "邮件" ||
      invoiceForm.value.postal_type === "邮件且快递"
    ) {
      if (!invoiceForm.value.email_address) {
        invoiceFieldErrors.value.email_address = "邮箱地址不能为空";
        hasValidationError = true;
      }
    }

    if (hasValidationError) {
      toast.add({
        severity: "error",
        summary: "验证错误",
        detail: "请填写所有必填字段",
        life: 3000,
      });
      return;
    }

    const formData = {
      customer_invoice_name: invoiceForm.value.customer_invoice_name,
      customer_deposit_bank: invoiceForm.value.customer_deposit_bank,
      customer_deposit_bank_sub: invoiceForm.value.customer_deposit_bank_sub,
      customer_bank_account_name: invoiceForm.value.customer_bank_account_name,
      customer_bank_account: invoiceForm.value.customer_bank_account,
      customer_tax_number: invoiceForm.value.customer_tax_number,
      customer_invoice_type: invoiceForm.value.customer_invoice_type,
      customer_receive_explain: invoiceForm.value.customer_receive_explain,
      customer_verify_type: invoiceForm.value.customer_verify_type,
      customer_fare_order: invoiceForm.value.customer_fare_order,
      is_unusual_need: invoiceForm.value.is_unusual_need,
      unusual_need_explain: invoiceForm.value.unusual_need_explain,
      is_open_charge: invoiceForm.value.is_open_charge,
      postal_type: invoiceForm.value.postal_type,
      postal_address: invoiceForm.value.postal_address,
      email_address: invoiceForm.value.email_address,
      phone_number: invoiceForm.value.phone_number,
    };

    await createAccountSeqInvoice(selectedAccountSeq.value.id, formData);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "发票信息创建成功",
      life: 3000,
    });

    invoiceDrawerVisible.value = false;
    loadInvoices();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data?.fields) {
      const fields = error.response.data.data.fields;
      invoiceFieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        invoiceFieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "发票信息创建失败",
        life: 3000,
      });
    }
  } finally {
    isSubmittingInvoice.value = false;
  }
};

// 调整发票优先级
const handlePriorityToggle = async (invoice: InvoiceInfoItem) => {
  if (!selectedAccountSeq.value) return;

  // 检查是否只有一条发票信息
  if (invoices.value.length <= 1) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "当前分账序号下只有一条发票信息，无法调整优先级",
      life: 3000,
    });
    return;
  }

  // 检查是否为高优先级发票
  if (invoice.priority === 1) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "不可对优先级为高的发票信息进行操作",
      life: 3000,
    });
    return;
  }

  try {
    await adjustInvoicePriority(selectedAccountSeq.value.id, invoice.id!);
    toast.add({
      severity: "success",
      summary: "成功",
      detail: "优先级调整成功",
      life: 3000,
    });
    loadInvoices(); // 重新加载列表
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "优先级调整失败",
      life: 3000,
    });
  }
};
</script>

<template>
  <div class="account-seq-invoice-container">
    <Toast />

    <!-- 使用Splitter组件分栏显示 -->
    <Splitter style="height: calc(100vh - 9rem)">
      <!-- 左侧：分账序号列表 (35%) -->
      <SplitterPanel :size="35" :minSize="30" class="account-seq-panel">
        <div class="card h-full">
          <Toolbar class="mb-2">
            <template #start>
              <Message
                variant="simple"
                severity="info"
                icon="pi pi-sort-numeric-up-alt"
              >
                分账序号
              </Message>
            </template>
            <template #end>
              <Select
                v-model="selectedCustomerNum"
                :options="customerOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="请选择客户"
                filter
                showClear
                class="w-48 mr-2"
              />
              <FloatLabel class="mr-2">
                <InputText
                  id="seqNameFilter"
                  v-model="seqNameFilter"
                  class="w-48"
                />
                <label for="seqNameFilter">分账序号名称</label>
              </FloatLabel>
              <Button
                rounded
                outlined
                icon="pi pi-search"
                @click="loadAccountSeqs"
              />
              <Divider layout="vertical" />
              <Button
                label="新建"
                icon="pi pi-plus"
                outlined
                @click="openNew"
                :disabled="!hasOperationPermission"
              />
            </template>
          </Toolbar>

          <!-- 分账序号数据表格 -->
          <DataTable
            :value="accountSeqs"
            :loading="loading"
            :lazy="true"
            :paginator="true"
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            :totalRecords="totalRecords"
            @page="onPage($event)"
            showGridlines
            stripedRows
            scrollable
            scrollHeight="calc(100vh - 21rem)"
            selectionMode="single"
            v-model:selection="selectedAccountSeq"
            @row-select="onAccountSeqSelect"
            @row-unselect="onAccountSeqUnselect"
            class="account-seq-table p-datatable-sm"
            resizableColumns
            columnResizeMode="fit"
          >
            <template #empty>
              <div class="empty-message">
                <i
                  class="pi pi-inbox"
                  style="
                    font-size: 2rem;
                    color: var(--p-text-color-secondary);
                    margin-bottom: 0.5rem;
                  "
                ></i>
                <p>暂无分账序号数据</p>
              </div>
            </template>

            <Column
              field="account_seq"
              header="分账序号"
              style="min-width: 10rem"
            >
              <template #body="{ data }">
                <Message variant="simple" severity="info" class="font-medium">
                  {{ data.account_seq }}
                </Message>
              </template>
            </Column>

            <Column field="seq_name" header="序号名称" style="min-width: 10rem">
              <template #body="{ data }">
                <span class="font-mono text-sm">{{ data.seq_name }}</span>
              </template>
            </Column>

            <Column
              field="customer_name"
              header="客户名称"
              style="min-width: 10rem; max-width: 10rem"
            >
              <template #body="{ data }">
                <span
                  class="font-mono omitted-text"
                  :title="data.customer_name"
                  v-tooltip.top="data.customer_name"
                >
                  {{ data.customer_name }}
                </span>
              </template>
            </Column>

            <Column field="tax" header="税率" style="min-width: 6rem">
              <template #body="{ data }">
                <Message variant="simple" :severity="getTaxSeverity(data.tax)">
                  {{ data.tax }}%
                </Message>
              </template>
            </Column>
          </DataTable>
        </div>
      </SplitterPanel>

      <!-- 右侧：发票信息列表 (65%) -->
      <SplitterPanel :size="65" :minSize="60" class="invoice-panel">
        <div class="card h-full">
          <Toolbar class="mb-2">
            <template #start>
              <div class="flex align-items-center gap-2">
                <Message variant="simple" severity="info">
                  发票信息{{
                    selectedAccountSeq
                      ? `(${selectedAccountSeq.account_seq})`
                      : ""
                  }}
                </Message>
              </div>
            </template>
            <template #end>
              <div class="flex align-items-center gap-2">
                <FloatLabel>
                  <InputText v-model="selectedCreateUser" />
                  <label>创建者</label>
                </FloatLabel>
                <FloatLabel>
                  <InputText v-model="selectedCustomerInvoiceName" />
                  <label>发票名称</label>
                </FloatLabel>
                <Button
                  icon="pi pi-search"
                  @click="handleInvoiceSearch"
                  outlined
                  rounded
                />
                <Button
                  icon="pi pi-refresh"
                  @click="resetInvoiceFilters"
                  outlined
                  rounded
                  severity="secondary"
                />
              </div>
              <Divider layout="vertical" />
              <Button
                label="新建"
                icon="pi pi-plus"
                outlined
                @click="createNewInvoice"
                :disabled="!hasOperationPermission || !selectedAccountSeq"
              />
            </template>
          </Toolbar>

          <!-- 发票数据表格 -->
          <div v-if="!selectedAccountSeq" class="empty-message">
            <i
              class="pi pi-info-circle"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>请先选择左侧的分账序号</p>
          </div>

          <DataTable
            v-else
            :value="invoices"
            :loading="invoiceLoading"
            :lazy="true"
            :paginator="true"
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            :totalRecords="invoiceTotalRecords"
            @page="onInvoicePage($event)"
            showGridlines
            stripedRows
            scrollable
            scrollHeight="calc(100vh - 21rem)"
            class="p-datatable-sm"
            resizableColumns
            columnResizeMode="fit"
          >
            <template #empty>
              <div class="empty-message">
                <i
                  class="pi pi-inbox"
                  style="
                    font-size: 2rem;
                    color: var(--p-text-color-secondary);
                    margin-bottom: 1rem;
                  "
                ></i>
                <p>暂无发票数据</p>
              </div>
            </template>
            <Column
              field="customer_invoice_name"
              header="开票名称"
              style="min-width: 15rem"
            >
              <template #body="{ data }">
                <span class="font-medium">{{
                  data.customer_invoice_name
                }}</span>
              </template>
            </Column>
            <Column
              field="customer_bank_account_name"
              header="银行账户名"
              style="min-width: 15rem"
            >
              <template #body="{ data }">
                <span class="font-mono text-sm">{{
                  data.customer_bank_account_name
                }}</span>
              </template>
            </Column>
            <Column
              field="customer_bank_account"
              header="银行账号"
              style="min-width: 15rem"
            >
              <template #body="{ data }">
                <span class="font-mono text-sm">{{
                  data.customer_bank_account
                }}</span>
              </template>
            </Column>
            <Column
              field="customer_tax_number"
              header="税号"
              style="min-width: 15rem"
            >
              <template #body="{ data }">
                <span class="font-mono text-sm">{{
                  data.customer_tax_number
                }}</span>
              </template>
            </Column>
            <Column
              field="customer_invoice_type"
              header="发票类型"
              style="min-width: 8rem"
            >
              <template #body="{ data }">
                <Tag
                  :value="data.customer_invoice_type"
                  :severity="getInvoiceTypeSeverity(data.customer_invoice_type)"
                />
              </template>
            </Column>
            <Column
              field="customer_verify_type"
              header="对账类型"
              style="min-width: 8rem"
            >
              <template #body="{ data }">
                <Tag
                  :value="data.customer_verify_type"
                  :severity="
                    getCustomerVerifyTypeSeverity(data.customer_verify_type)
                  "
                />
              </template>
            </Column>
            <Column
              field="customer_fare_order"
              header="票款顺序"
              style="min-width: 8rem"
            >
              <template #body="{ data }">
                <Tag
                  :value="data.customer_fare_order"
                  :severity="
                    data.customer_fare_order === '先票后款'
                      ? 'info'
                      : 'secondary'
                  "
                />
              </template>
            </Column>
            <Column field="priority" header="优先级" style="min-width: 8rem">
              <template #body="{ data }">
                <ToggleButton
                  :modelValue="data.priority === 1"
                  onLabel="高"
                  offLabel="低"
                  onIcon="pi pi-arrow-up"
                  offIcon="pi pi-arrow-down"
                  :disabled="invoices.length <= 1 || data.priority === 1"
                  @click="handlePriorityToggle(data)"
                  :style="
                    data.priority === 1
                      ? 'background: var(--p-orange-500) !important; color: var(--p-orange-600);'
                      : 'background: var(--p-surface-100) !important; color: var(--p-text-color-secondary);'
                  "
                />
              </template>
            </Column>
            <Column
              field="create_user"
              header="创建者"
              style="min-width: 6rem"
            />
            <Column
              field="created_at"
              header="创建时间"
              style="min-width: 12rem"
            >
              <template #body="{ data }">
                {{ formatDateTime(data.created_at) }}
              </template>
            </Column>
            <Column
              header="操作"
              :exportable="false"
              alignFrozen="right"
              frozen
              style="min-width: 5rem"
            >
              <template #body="{ data }">
                <Button
                  icon="pi pi-eye"
                  outlined
                  rounded
                  class="p-button-info"
                  @click="viewInvoice(data)"
                  v-tooltip.top="'查看发票信息详情'"
                />
              </template>
            </Column>
          </DataTable>
        </div>
      </SplitterPanel>
    </Splitter>
  </div>

  <!-- 新建/编辑分账序号抽屉 -->
  <Drawer
    v-model:visible="accountSeqDrawerVisible"
    position="left"
    :style="{ width: '70rem' }"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :header="'新增分账序号'"
    class="account-seq-drawer"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="customer_id" class="required"> 客户 </label>
                <Select
                  v-model="accountSeqForm.customer_id"
                  :options="customerOptions"
                  optionLabel="label"
                  optionValue="customer_id"
                  placeholder="请选择客户"
                  filter
                  :class="['w-full', { 'p-invalid': fieldErrors.customer_id }]"
                />
                <Message
                  v-if="fieldErrors.customer_id"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.customer_id }}
                </Message>
              </div>
              <div class="field">
                <label for="tax" class="required"> 税率 </label>
                <Select
                  v-model="accountSeqForm.tax"
                  :options="taxOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="请选择税率"
                  :class="['w-full', { 'p-invalid': fieldErrors.tax }]"
                />
                <Message
                  v-if="fieldErrors.tax"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.tax }}
                </Message>
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 分账序号信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">分账序号信息</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="seq_name" class="required"> 分账序号名称 </label>
                <InputText
                  v-model="accountSeqForm.seq_name"
                  required
                  :class="['w-full', { 'p-invalid': fieldErrors.seq_name }]"
                />
                <Message
                  v-if="fieldErrors.seq_name"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.seq_name }}
                </Message>
              </div>
            </div>
          </Fluid>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="accountSeqDrawerVisible = false"
        />
        <Button label="确认" icon="pi pi-check" @click="saveAccountSeq" />
      </div>
    </template>
  </Drawer>

  <!-- 新建发票信息抽屉 -->
  <Drawer
    v-model:visible="invoiceDrawerVisible"
    position="right"
    :style="{ width: '80rem' }"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    header="新建发票信息"
    class="invoice-drawer p-fluid"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label class="required">分账序号</label>
                <InputText :value="selectedAccountSeq?.account_seq" disabled />
              </div>
              <div class="field">
                <label for="customer_invoice_name" class="required"
                  >开票名称</label
                >
                <InputText
                  id="customer_invoice_name"
                  v-model="invoiceForm.customer_invoice_name"
                  :class="{
                    'p-invalid': invoiceFieldErrors.customer_invoice_name,
                  }"
                />
                <Message
                  v-if="invoiceFieldErrors.customer_invoice_name"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ invoiceFieldErrors.customer_invoice_name }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_tax_number" class="required">税号</label>
                <InputText
                  id="customer_tax_number"
                  v-model="invoiceForm.customer_tax_number"
                  :class="{
                    'p-invalid': invoiceFieldErrors.customer_tax_number,
                  }"
                />
                <Message
                  v-if="invoiceFieldErrors.customer_tax_number"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ invoiceFieldErrors.customer_tax_number }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_invoice_type" class="required"
                  >发票类型</label
                >
                <Select
                  id="customer_invoice_type"
                  v-model="invoiceForm.customer_invoice_type"
                  :options="[
                    { label: '增值税专票', value: '增值税专票' },
                    { label: '增值税普票', value: '增值税普票' },
                    { label: 'invoice', value: 'invoice' },
                  ]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择发票类型"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 银行信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">银行信息</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="customer_deposit_bank" class="required"
                  >开户银行</label
                >
                <InputText
                  id="customer_deposit_bank"
                  v-model="invoiceForm.customer_deposit_bank"
                />
                <Message
                  v-if="invoiceFieldErrors.customer_deposit_bank"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ invoiceFieldErrors.customer_deposit_bank }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_deposit_bank_sub" class="required"
                  >开户支行</label
                >
                <InputText
                  id="customer_deposit_bank_sub"
                  v-model="invoiceForm.customer_deposit_bank_sub"
                />
                <Message
                  v-if="invoiceFieldErrors.customer_deposit_bank_sub"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ invoiceFieldErrors.customer_deposit_bank_sub }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_bank_account_name" class="required"
                  >银行账户名</label
                >
                <InputText
                  id="customer_bank_account_name"
                  v-model="invoiceForm.customer_bank_account_name"
                />
                <Message
                  v-if="invoiceFieldErrors.customer_bank_account_name"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ invoiceFieldErrors.customer_bank_account_name }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_bank_account" class="required"
                  >银行账号</label
                >
                <InputText
                  id="customer_bank_account"
                  v-model="invoiceForm.customer_bank_account"
                />
                <Message
                  v-if="invoiceFieldErrors.customer_bank_account"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ invoiceFieldErrors.customer_bank_account }}
                </Message>
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 业务设置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">业务设置</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="customer_verify_type" class="required"
                  >对账类型</label
                >
                <Select
                  id="customer_verify_type"
                  v-model="invoiceForm.customer_verify_type"
                  :options="[
                    { label: '不对账', value: '不对账' },
                    { label: '对账无需确认', value: '对账无需确认' },
                    { label: '对账超期无需确认', value: '对账超期无需确认' },
                    { label: '对账要确认', value: '对账要确认' },
                  ]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择对账类型"
                />
              </div>
              <div class="field">
                <label for="customer_fare_order" class="required"
                  >票款顺序</label
                >
                <Select
                  id="customer_fare_order"
                  v-model="invoiceForm.customer_fare_order"
                  :options="[
                    { label: '先票后款', value: '先票后款' },
                    { label: '先款后票', value: '先款后票' },
                  ]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择票款顺序"
                />
              </div>
              <div class="field">
                <label for="is_open_charge" class="required">开账状态</label>
                <Select
                  id="is_open_charge"
                  v-model="invoiceForm.is_open_charge"
                  :options="[
                    { label: '暂停', value: '暂停' },
                    { label: '开账', value: '开账' },
                    { label: '终止', value: '终止' },
                  ]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择开账状态"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 特殊需求 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">特殊需求</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="is_unusual_need" class="required"
                  >是否有特殊需求</label
                >
                <Select
                  id="is_unusual_need"
                  v-model="invoiceForm.is_unusual_need"
                  :options="[
                    { label: '是', value: '是' },
                    { label: '否', value: '否' },
                  ]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择是否有特殊需求"
                />
              </div>
              <div class="field" v-if="invoiceForm.is_unusual_need === '是'">
                <label for="unusual_need_explain">特殊需求说明</label>
                <InputText
                  id="unusual_need_explain"
                  v-model="invoiceForm.unusual_need_explain"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 邮寄信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">邮寄信息</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="postal_type" class="required">邮寄方式</label>
                <Select
                  id="postal_type"
                  v-model="invoiceForm.postal_type"
                  :options="[
                    { label: '邮件', value: '邮件' },
                    { label: '快递', value: '快递' },
                    { label: '邮件且快递', value: '邮件且快递' },
                    { label: '无需', value: '无需' },
                  ]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择邮寄方式"
                />
              </div>
              <div class="grid grid-cols-3 gap-4">
                <div
                  class="field"
                  v-if="
                    invoiceForm.postal_type === '快递' ||
                    invoiceForm.postal_type === '邮件且快递'
                  "
                >
                  <label for="postal_address">邮寄地址</label>
                  <InputText
                    id="postal_address"
                    v-model="invoiceForm.postal_address"
                    :class="{ 'p-invalid': invoiceFieldErrors.postal_address }"
                  />
                  <Message
                    v-if="invoiceFieldErrors.postal_address"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ invoiceFieldErrors.postal_address }}
                  </Message>
                </div>
                <div
                  class="field"
                  v-if="
                    invoiceForm.postal_type === '邮件' ||
                    invoiceForm.postal_type === '邮件且快递'
                  "
                >
                  <label for="email_address">邮箱地址</label>
                  <InputText
                    id="email_address"
                    v-model="invoiceForm.email_address"
                    :class="{ 'p-invalid': invoiceFieldErrors.email_address }"
                  />
                  <Message
                    v-if="invoiceFieldErrors.email_address"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ invoiceFieldErrors.email_address }}
                  </Message>
                </div>
                <div class="field">
                  <label for="phone_number">联系电话</label>
                  <InputText
                    id="phone_number"
                    v-model="invoiceForm.phone_number"
                    :class="{ 'p-invalid': invoiceFieldErrors.phone_number }"
                  />
                </div>
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">其他信息</h3>
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="customer_receive_explain">收票说明</label>
                <Textarea
                  id="customer_receive_explain"
                  v-model="invoiceForm.customer_receive_explain"
                  rows="3"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="invoiceDrawerVisible = false"
        />
        <Button
          label="确认"
          icon="pi pi-check"
          @click="saveInvoice"
          :loading="isSubmittingInvoice"
        />
      </div>
    </template>
  </Drawer>

  <!-- 发票信息详情弹框 -->
  <Dialog
    v-model:visible="invoiceDetailDialog"
    modal
    :header="`发票信息详情 - ${selectedInvoice?.customer_invoice_name || ''}`"
    :style="{ width: '80rem' }"
    class="invoice-detail-dialog"
    :closable="true"
    :dismissableMask="true"
    maximizable
  >
    <div v-if="selectedInvoice" class="invoice-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          基本信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>分账序号</label>
              <span class="detail-value">{{
                selectedInvoice.account_seq
              }}</span>
            </div>
            <div class="detail-item">
              <label>开票名称</label>
              <span class="detail-value">{{
                selectedInvoice.customer_invoice_name
              }}</span>
            </div>
            <div class="detail-item">
              <label>税号</label>
              <span class="detail-value">{{
                selectedInvoice.customer_tax_number
              }}</span>
            </div>
            <div class="detail-item">
              <label>发票类型</label>
              <span class="detail-value">{{
                selectedInvoice.customer_invoice_type
              }}</span>
            </div>
            <div class="detail-item">
              <label>对账类型</label>
              <span class="detail-value">{{
                selectedInvoice.customer_verify_type
              }}</span>
            </div>
            <div class="detail-item">
              <label>票款顺序</label>
              <span class="detail-value">{{
                selectedInvoice.customer_fare_order
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 银行信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-building"></i>
          银行信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>开户银行</label>
              <span class="detail-value">{{
                selectedInvoice.customer_deposit_bank || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>开户支行</label>
              <span class="detail-value">{{
                selectedInvoice.customer_deposit_bank_sub || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>银行账户名</label>
              <span class="detail-value">{{
                selectedInvoice.customer_bank_account_name || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>银行账号</label>
              <span class="detail-value">{{
                selectedInvoice.customer_bank_account || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 其他设置 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-cog"></i>
          其他设置
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>是否有特殊需求</label>
              <span class="detail-value">{{
                selectedInvoice.is_unusual_need
              }}</span>
            </div>
            <div
              class="detail-item"
              v-if="selectedInvoice.is_unusual_need === '是'"
            >
              <label>特殊需求说明</label>
              <span class="detail-value">{{
                selectedInvoice.unusual_need_explain || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>开账状态</label>
              <span class="detail-value">{{
                selectedInvoice.is_open_charge
              }}</span>
            </div>
            <div class="detail-item">
              <label>邮寄方式</label>
              <span class="detail-value">{{
                selectedInvoice.postal_type
              }}</span>
            </div>
            <div
              class="detail-item"
              v-if="
                selectedInvoice.postal_type === '快递' ||
                selectedInvoice.postal_type === '邮件且快递'
              "
            >
              <label>邮寄地址</label>
              <span class="detail-value">{{
                selectedInvoice.postal_address || "--"
              }}</span>
            </div>
            <div
              class="detail-item"
              v-if="
                selectedInvoice.postal_type === '邮件' ||
                selectedInvoice.postal_type === '邮件且快递'
              "
            >
              <label>邮箱地址</label>
              <span class="detail-value">{{
                selectedInvoice.email_address || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>联系电话</label>
              <span class="detail-value">{{
                selectedInvoice.phone_number || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>收票说明</label>
              <span class="detail-value">{{
                selectedInvoice.customer_receive_explain || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>创建者</label>
              <span class="detail-value">{{
                selectedInvoice.create_user
              }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间</label>
              <span class="detail-value">{{
                formatDateTime(selectedInvoice.created_at || "")
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="关闭"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="invoiceDetailDialog = false"
          size="large"
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.account-seq-invoice-container {
  padding: 1rem;
  background: #f8f9fa;
}

/* DataTable选中行样式 */
:deep(.account-seq-table .p-datatable-tbody > tr.p-datatable-row-selected) {
  background: var(--p-primary-100) !important;
  border-color: var(--p-primary-color) !important;
}

:deep(
    .account-seq-table .p-datatable-tbody > tr.p-datatable-row-selected:hover
  ) {
  background: var(--p-primary-100) !important;
}

/* 发票详情弹框样式 */
.invoice-detail-content {
  padding: 0.5rem 0;
}

.detail-section {
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #6e6e73;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem;
}

/* 表单分段样式 - 参考OrderList.vue */
.form-section {
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--p-surface-card);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--p-primary-color);
}

.section-title i {
  color: var(--p-primary-color);
  font-size: 1rem;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 分账序号抽屉样式 - 参考OrderList.vue */
:deep(.invoice-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

:deep(.account-seq-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* 表单验证错误样式 - 参考OrderList.vue */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

/* 按钮样式 */
:deep(.p-button) {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

:deep(.p-button:hover) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.p-button:active) {
  transform: scale(0.98);
}

:deep(.p-inputtext:focus) {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.25);
  border-color: #007aff;
}

/* 发票详情弹框样式 */
:deep(.invoice-detail-dialog) {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
}

:deep(.invoice-detail-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
}

:deep(.invoice-detail-dialog .p-dialog-title) {
  font-weight: 600 !important;
  font-size: 1.25rem !important;
  color: #1d1d1f !important;
}

:deep(.invoice-detail-dialog .p-dialog-content) {
  padding: 2rem !important;
  background: #ffffff !important;
}

:deep(.invoice-detail-dialog .p-dialog-footer) {
  background: #f8f9fa !important;
  border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
}

/* Splitter样式 */
:deep(.p-splitter) {
  border: none;
  background: transparent;
}

:deep(.p-splitter-panel) {
  background: transparent;
}

:deep(.p-splitter-gutter) {
  background: var(--p-surface-border);
  transition: background-color 0.2s;
}

:deep(.p-splitter-gutter:hover) {
  background: var(--p-primary-500);
}

:deep(.p-splitter-gutter-handle) {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-seq-invoice-container {
    padding: 0.5rem;
  }
}

.omitted-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  font-size: 0.875rem;
}
</style>
