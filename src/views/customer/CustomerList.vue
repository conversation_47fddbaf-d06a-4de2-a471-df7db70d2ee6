<script setup lang="ts">
import { useToast } from "primevue/usetoast";
import { onMounted, ref, computed } from "vue";
import { usePermission } from "../../composables/usePermission";
import {
  createCustomer,
  CustomersInfoParams,
  getCustomersInfo,
  getSales,
  updateCustomer,
  approveCustomer,
  getCustomerContactsList,
  createCustomerContact,
  updateCustomerContact,
} from "../../services/customer";
import type {
  CustomersInfoItem,
  ContactInfo,
  CustomerFormData,
  ContactFormData,
} from "../../types/customer";
import {
  businessOptions,
  customerClassOptions,
  customerTypeOptions,
  statusOptions,
  tradeTypeOptions,
  contactClassOptions,
  contactTypeOptions,
  ownTypeOptions,
  customerStateSeverityMap,
  customerApproveStateSeverityMap,
  customerApproveStateValueMap,
  customerApproveActionTypes,
  customerApproveActionLabels,
} from "../../utils/const";
import { countries } from "../../utils/basic";
import { initNumber } from "../../utils/common";
import { useConfirm } from "primevue/useconfirm";
import ApproveHistory from "./ApproveHistory.vue";
import CustomerDetail from "./CustomerDetail.vue";

const customers = ref<CustomersInfoItem[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选相关
const selectedFilterColumn = ref("");
const filterColumnOptions = [
  { label: "--", value: "--" },
  { label: "客户名称", value: "customer_name" },
  { label: "销售名称", value: "sale_name" },
  { label: "创建人", value: "create_user" },
];
const filterValue = ref("");

// 加载客户列表数据
const loadCustomers = async () => {
  try {
    loading.value = true;
    const params: CustomersInfoParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (selectedFilterColumn.value !== "--" && filterValue.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }

    const response = await getCustomersInfo(params);
    customers.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "加载客户列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadCustomers();
};

onMounted(async () => {
  await initializeUserInfo();
  loadCustomers();
});

// 表单相关
const customerDrawerVisible = ref(false);
const customerForm = ref<CustomerFormData>({
  customer_num: "",
  customer_name: "",
  customer_name_intl: "",
  group_name: "",
  customer_usci: "",
  like_key: "",
  sale_name: "",
  customer_class: "",
  customer_type: "",
  trade_type: "",
  business: "",
  remark: "",
  upload_file: "",
  state: "",
  country: "",
  province: "",
  city: "",
});

const salesOptions = ref<{ label: string; value: string }[]>([]);
const fileUpload = ref();

// 加载销售列表
const loadSales = async () => {
  try {
    const response = await getSales();
    salesOptions.value = response.map((item) => ({
      label: item.sale_name,
      value: item.sale_name,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "加载销售列表失败",
      life: 3000,
    });
  }
};

// 打开新建对话框
const openNew = async () => {
  const customerNum = await initNumber("SRHT");
  customerForm.value = {
    customer_num: customerNum,
    customer_name: "",
    customer_name_intl: "",
    group_name: "",
    customer_usci: "",
    like_key: "",
    sale_name: "",
    customer_class: "",
    customer_type: "",
    trade_type: "",
    business: "",
    remark: "",
    upload_file: "",
    state: "",
    country: "",
    province: "",
    city: "",
  };
  customerDrawerVisible.value = true;
  fieldErrors.value = {};
  loadSales();
};

// 打开编辑对话框
const openEdit = (customer: CustomersInfoItem) => {
  if (!hasOperationPermission.value) return;

  // 检查是否可以编辑客户信息
  if (!canEditCustomer(customer.approve_state)) {
    toast.add({
      severity: "warn",
      summary: "警告",
      detail: "当前审批状态下不能编辑客户信息",
      life: 3000,
    });
    return;
  }

  customerForm.value = { ...customer };
  customerDrawerVisible.value = true;
  fieldErrors.value = {};
  loadSales();
};

// 表单验证
const validateCustomerForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "customer_name", label: "客户全称" },
    { key: "sale_name", label: "执行销售" },
    { key: "group_name", label: "所属集团" },
    { key: "customer_usci", label: "统一社会信用代码" },
    { key: "like_key", label: "客户简称" },
    { key: "customer_class", label: "客户类别" },
    { key: "customer_type", label: "客户类型" },
    { key: "trade_type", label: "行业类型" },
    { key: "business", label: "归属业务" },
    { key: "state", label: "客户状态" },
  ];

  requiredFields.forEach((field) => {
    const value = customerForm.value[field.key as keyof CustomerFormData];
    if (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "") ||
      (typeof value === "number" && isNaN(value))
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  return isValid;
};

// 保存客户信息
const fieldErrors = ref<{ [key: string]: string }>({});
const saveCustomer = async () => {
  // 验证表单
  if (!validateCustomerForm()) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
    return;
  }

  try {
    if (customerForm.value.id) {
      // 编辑客户信息
      await updateCustomer(customerForm.value, customerForm.value.id);
    } else {
      // 新增客户信息
      await createCustomer(customerForm.value);
    }
    customerDrawerVisible.value = false;
    toast.add({
      severity: "success",
      summary: "成功",
      detail: customerForm.value.id ? "客户信息更新成功" : "客户信息创建成功",
      life: 3000,
    });
    loadCustomers();
  } catch (error: any) {
    if (error.response.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: customerForm.value.id ? "客户信息更新失败" : "客户信息创建失败",
        life: 3000,
      });
    }
  }
};

// 联系人相关
const contactDrawerVisible = ref(false);
const contactForm = ref<ContactFormData>({
  name: "",
  company: "",
  position: "",
  address: "",
  phone: "",
  mobile_phone: "",
  email: "",
  contact_class: "",
  contact_type: "",
  own_type: "",
  remark: "",
});
const selectedCustomerId = ref<number>();
const selectedContactId = ref<number>();

// 修改为三种模式：create(创建)、edit(编辑)
const contactMode = ref<"create" | "edit">("create");

// 行扩展相关
const expandedRows = ref({});
const contactsMap = ref<Record<number, ContactInfo[]>>({});

// 获取客户联系人列表
const loadCustomerContacts = async (customerId: number) => {
  try {
    const response = await getCustomerContactsList(customerId);
    contactsMap.value[customerId] = response.data;
    return response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取客户联系人列表失败",
      life: 3000,
    });
    return [];
  }
};

// 行展开事件处理
const onRowExpand = async (event: any) => {
  const customerId = event.data.id;
  await loadCustomerContacts(customerId);
};

// 表单验证相关正则表达式
const PHONE_RE = /^(0[1-9]\d{1,2})-?(\d{7,8})$/;
const EMAIL_RE = /^[a-zA-Z0-9_.%+-]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
const MOBILE_PHONE_RE = /^1[3-9]\d{9}$/;

// 验证联系人表单
const validateContactForm = () => {
  const errors: { [key: string]: string } = {};

  // 必填项验证
  if (!contactForm.value.name) {
    errors.name = "联系人姓名为必填项";
  }
  if (!contactForm.value.contact_class) {
    errors.contact_class = "联系人类别为必填项";
  }
  if (!contactForm.value.contact_type) {
    errors.contact_type = "联系人类型为必填项";
  }
  if (!contactForm.value.own_type) {
    errors.own_type = "归属类型为必填项";
  }

  // 格式验证
  if (contactForm.value.phone && !PHONE_RE.test(contactForm.value.phone)) {
    errors.phone = "电话格式不正确, 例如: 021-12345678";
  }
  if (
    contactForm.value.mobile_phone &&
    !MOBILE_PHONE_RE.test(contactForm.value.mobile_phone)
  ) {
    errors.mobile_phone = "手机号格式不正确";
  }
  if (contactForm.value.email && !EMAIL_RE.test(contactForm.value.email)) {
    errors.email = "邮箱格式不正确";
  }

  contactFieldErrors.value = errors;
  return Object.keys(errors).length === 0;
};

const contactFieldErrors = ref<{ [key: string]: string }>({});

// 查看或创建客户联系人
const handleContactInfo = async (customer: CustomersInfoItem) => {
  selectedCustomerId.value = customer.id;

  // 新建联系人信息
  contactMode.value = "create";
  contactForm.value = {
    name: "",
    company: "",
    position: "",
    address: "",
    phone: "",
    mobile_phone: "",
    email: "",
    contact_class: "",
    contact_type: "",
    own_type: "",
    remark: "",
  };
  contactFieldErrors.value = {}; // 清空错误信息
  contactDrawerVisible.value = true;
};

// 编辑联系人
const editContact = (customer: CustomersInfoItem, contact: ContactFormData) => {
  selectedCustomerId.value = customer.id;
  selectedContactId.value = contact.id;

  contactMode.value = "edit";
  contactForm.value = { ...contact };
  contactFieldErrors.value = {}; // 清空错误信息
  contactDrawerVisible.value = true;
};

// 保存联系人信息
const saveContactInfo = async () => {
  if (!selectedCustomerId.value) return;

  // 验证表单
  if (!validateContactForm()) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: Object.values(contactFieldErrors.value).join("; "),
      life: 3000,
    });
    return;
  }

  try {
    const customerId = selectedCustomerId.value;

    if (contactMode.value === "edit" && selectedContactId.value) {
      // 更新联系人
      await updateCustomerContact(
        customerId,
        selectedContactId.value,
        contactForm.value
      );
    } else {
      // 新建联系人
      await createCustomerContact(customerId, contactForm.value);
    }

    contactDrawerVisible.value = false;
    toast.add({
      severity: "success",
      summary: "成功",
      detail:
        contactMode.value === "edit"
          ? "客户联系人信息更新成功"
          : "客户联系人信息创建成功",
      life: 3000,
    });
    // 刷新联系人列表
    if (contactsMap.value[customerId]) {
      await loadCustomerContacts(customerId);
    }
    loadCustomers(); // 刷新客户列表
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "保存客户联系人信息失败",
      life: 3000,
    });
  }
};

// 国家、省份、城市联动
const provincesOptions = computed(() => {
  if (!customerForm.value.country) return [];
  const selectedCountry = countries.find(
    (country) => country.value === customerForm.value.country
  );
  return selectedCountry
    ? selectedCountry.provinces.map((province) => ({
        label: province.label,
        value: province.value,
      }))
    : [];
});

const citiesOptions = computed(() => {
  if (!customerForm.value.country || !customerForm.value.province) return [];
  const selectedCountry = countries.find(
    (country) => country.value === customerForm.value.country
  );
  if (!selectedCountry) return [];

  const selectedProvince = selectedCountry.provinces.find(
    (province) => province.value === customerForm.value.province
  );
  return selectedProvince
    ? selectedProvince.cities.map((city) => ({
        label: city.label,
        value: city.value,
      }))
    : [];
});

// 监听国家变化，重置省份和城市
const handleCountryChange = () => {
  customerForm.value.province = "";
  customerForm.value.city = "";
};

// 监听省份变化，重置城市
const handleProvinceChange = () => {
  customerForm.value.city = "";
};

// 检查是否可编辑客户信息
const canEditCustomer = (approveState: string) => {
  return ["init", "revoke", "back"].includes(approveState);
};

// 初始化确认框
const confirm = useConfirm();

// 审批历史记录相关
const approveHistoryVisible = ref(false);
const selectedCustomerForHistory = ref<number | null>(null);
const selectedCustomerForHistoryName = ref<string | null>(null);

// 客户详情相关
const customerDetailVisible = ref(false);
const selectedCustomerForDetail = ref<CustomersInfoItem | null>(null);

// 打开客户详情弹框
const openCustomerDetail = (customer: CustomersInfoItem) => {
  selectedCustomerForDetail.value = customer;
  customerDetailVisible.value = true;
};

// 打开审批历史记录抽屉
const openApproveHistory = (customerId: number, customerName: string) => {
  selectedCustomerForHistory.value = customerId;
  selectedCustomerForHistoryName.value = customerName;
  approveHistoryVisible.value = true;
};

// 关闭审批历史记录抽屉
const closeApproveHistory = () => {
  approveHistoryVisible.value = false;
  selectedCustomerForHistory.value = null;
  selectedCustomerForHistoryName.value = null;
};

// 执行审批操作
const confirmApprove = (customerId: number, action: string, event: Event) => {
  confirm.require({
    target: event.currentTarget as HTMLElement,
    header: "确认此步操作",
    message: `确定要${
      customerApproveActionLabels[
        action as keyof typeof customerApproveActionLabels
      ]
    }吗？`,
    icon: "pi pi-exclamation-triangle",
    rejectProps: {
      label: "取消",
      severity: "secondary",
    },
    acceptProps: {
      label: "确认",
      severity: "info",
    },
    accept: () => {
      approve(customerId, action);
    },
  });
};

// 添加approve_state相关逻辑
const approve = async (
  customerId: number,
  action: string,
  reason: string | null = null
) => {
  try {
    // 调用后端API
    await approveCustomer({
      id: customerId,
      action: action,
      reason: reason || undefined,
    });
    toast.add({
      severity: "success",
      summary: "成功",
      detail: `${
        customerApproveActionLabels[
          action as keyof typeof customerApproveActionLabels
        ]
      }成功`,
      life: 3000,
    });
    loadCustomers(); // 刷新客户列表
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: `${
        customerApproveActionLabels[
          action as keyof typeof customerApproveActionLabels
        ]
      }失败`,
      life: 3000,
    });
  }
};
</script>

<template>
  <div class="customer-list-container">
    <Toast />
    <ConfirmPopup />
    <div class="card">
      <!-- 筛选框 -->
      <Toolbar class="mb-2">
        <template #end>
          <FloatLabel class="mr-2">
            <Select
              v-model="selectedFilterColumn"
              :options="filterColumnOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="请选择筛选字段"
              style="width: 15rem"
              size="normal"
            />
          </FloatLabel>
          <FloatLabel class="mr-2">
            <InputText id="filterValue" v-model="filterValue" />
            <label for="filterValue">搜索值</label>
          </FloatLabel>
          <Button icon="pi pi-search" outlined rounded @click="loadCustomers" />
          <Divider layout="vertical" />
          <Button
            label="新建"
            icon="pi pi-plus"
            outlined
            @click="openNew"
            :disabled="!hasOperationPermission"
          />
        </template>
      </Toolbar>

      <DataTable
        :value="customers"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        :loading="loading"
        @page="onPage($event)"
        showGridlines
        scrollable
        scrollHeight="calc(100vh - 20rem)"
        class="p-datatable-sm"
        v-model:expandedRows="expandedRows"
        @row-expand="onRowExpand"
        rowGroupMode="subheader"
        dataKey="id"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无客户数据</p>
          </div>
        </template>
        <template #expansion="slotProps">
          <div class="p-3">
            <div class="flex justify-between items-center mb-3">
              <Message severity="info" variant="simple">
                {{ slotProps.data.customer_name }} - 联系人列表
              </Message>
            </div>
            <DataTable
              :value="contactsMap[slotProps.data.id] || []"
              stripedRows
              showGridlines
              class="p-datatable-sm"
            >
              <template #empty>
                <div class="empty-message">
                  <i
                    class="pi pi-inbox"
                    style="
                      font-size: 2rem;
                      color: var(--p-text-color-secondary);
                      margin-bottom: 1rem;
                    "
                  ></i>
                  <p>暂无联系人数据</p>
                </div>
              </template>
              <Column field="name" header="姓名">
                <template #body="contactSlot">
                  <Tag
                    :value="contactSlot.data.name"
                    :severity="
                      contactSlot.data.state === 'U' ? 'success' : 'danger'
                    "
                  />
                </template>
              </Column>
              <Column field="position" header="职位" />
              <Column field="company" header="公司" />
              <Column field="phone" header="电话" />
              <Column field="mobile_phone" header="手机" />
              <Column field="email" header="邮箱" />
              <Column field="address" header="地址" />
              <Column field="contact_class" header="联系人类别" />
              <Column field="contact_type" header="联系人类型" />
              <Column field="own_type" header="归属类型" />
              <Column header="操作" style="min-width: 8rem">
                <template #body="contactSlot">
                  <Button
                    icon="pi pi-pencil"
                    outlined
                    rounded
                    severity="info"
                    @click="editContact(slotProps.data, contactSlot.data)"
                    v-tooltip.top="'编辑联系人'"
                  />
                </template>
              </Column>
            </DataTable>
          </div>
        </template>
        <Column expander style="width: 3rem" />
        <Column field="customer_num" header="客户编号" style="min-width: 12rem">
          <template #body="slotProps">
            <span class="font-mono text-[#1890FF] text-sm">{{
              slotProps.data.customer_num
            }}</span>
          </template>
        </Column>
        <Column
          field="customer_name"
          header="客户名称"
          style="min-width: 15rem"
        >
          <template #body="slotProps">
            <span
              class="font-medium omitted-text"
              v-tooltip.top="slotProps.data.customer_name"
              >{{ slotProps.data.customer_name }}
            </span>
          </template>
        </Column>
        <Column field="group_name" header="客户分组" style="min-width: 8rem">
          <template #body="slotProps">
            <span class="font-mono text-sm">{{
              slotProps.data.group_name || "--"
            }}</span>
          </template>
        </Column>
        <Column
          field="customer_usci"
          header="统一社会信用代码"
          style="min-width: 10rem"
        >
          <template #body="slotProps">
            <span class="font-mono text-sm">{{
              slotProps.data.customer_usci || "--"
            }}</span>
          </template>
        </Column>
        <Column field="like_key" header="客户简称" style="min-width: 10rem">
          <template #body="slotProps">
            <span class="font-mono text-sm">{{
              slotProps.data.like_key || "--"
            }}</span>
          </template>
        </Column>
        <Column field="sale_name" header="销售名称" style="min-width: 8rem">
          <template #body="slotProps">
            <span class="font-mono text-sm">{{
              slotProps.data.sale_name || "--"
            }}</span>
          </template>
        </Column>
        <Column
          field="customer_class"
          header="客户类别"
          style="min-width: 8rem"
        >
          <template #body="slotProps">
            <span class="font-medium text-orange-600">{{
              slotProps.data.customer_class || "--"
            }}</span>
          </template>
        </Column>
        <Column field="customer_type" header="客户类型" style="min-width: 8rem">
          <template #body="slotProps">
            <span class="font-medium text-blue-600">{{
              slotProps.data.customer_type || "--"
            }}</span>
          </template>
        </Column>
        <Column field="trade_type" header="行业类型" style="min-width: 8rem">
          <template #body="slotProps">
            <span class="font-medium text-green-600">{{
              slotProps.data.trade_type || "--"
            }}</span>
          </template>
        </Column>
        <Column field="business" header="业务类型" style="min-width: 8rem">
          <template #body="slotProps">
            <span class="font-medium text-blue-600">{{
              slotProps.data.business || "--"
            }}</span>
          </template>
        </Column>
        <Column field="state" header="状态" style="min-width: 8rem">
          <template #body="{ data }">
            <Tag
              :severity="customerStateSeverityMap[data.state] || 'info'"
              :value="data.state"
            />
          </template>
        </Column>
        <Column field="approve_state" header="审批状态" style="min-width: 8rem">
          <template #body="{ data }">
            <Tag
              :severity="
                customerApproveStateSeverityMap[data.approve_state] || 'info'
              "
              :value="customerApproveStateValueMap[data.approve_state]"
            />
          </template>
        </Column>
        <Column field="create_user" header="创建人" style="min-width: 8rem">
          <template #body="slotProps">
            <span class="font-mono text-sm">{{
              slotProps.data.create_user || "--"
            }}</span>
          </template>
        </Column>
        <Column
          header="操作"
          :exportable="false"
          style="min-width: 15rem; white-space: nowrap"
          frozen
          alignFrozen="right"
        >
          <template #body="{ data }">
            <Button
              icon="pi pi-pencil"
              outlined
              rounded
              severity="success"
              :disabled="
                !hasOperationPermission || !canEditCustomer(data.approve_state)
              "
              @click="openEdit(data)"
              v-tooltip.top="'修改客户信息'"
              class="mr-2"
            />
            <Button
              icon="pi pi-eye"
              outlined
              rounded
              severity="info"
              @click="openCustomerDetail(data)"
              v-tooltip.top="'查看客户详细信息'"
              class="mr-2"
            />
            <Button
              icon="pi pi-user-plus"
              outlined
              rounded
              severity="success"
              @click="handleContactInfo(data)"
              v-tooltip.top="'新建联系人'"
              class="mr-2"
            />
            <Button
              v-if="data.approve_state === 'init'"
              icon="pi pi-check-circle"
              outlined
              rounded
              severity="info"
              @click="
                confirmApprove(
                  data.id,
                  customerApproveActionTypes.SUBMIT,
                  $event
                )
              "
              v-tooltip.top="customerApproveActionLabels.submit"
              class="mr-2"
              style="border-radius: 8px; transition: all 0.2s ease"
            />
            <Button
              v-if="data.approve_state === 'confirm'"
              icon="pi pi-times-circle"
              outlined
              rounded
              severity="warn"
              @click="
                confirmApprove(
                  data.id,
                  customerApproveActionTypes.REVOKE,
                  $event
                )
              "
              v-tooltip.top="customerApproveActionLabels.revoke"
              class="mr-2"
              style="border-radius: 8px; transition: all 0.2s ease"
            />
            <Button
              icon="pi pi-history"
              outlined
              rounded
              severity="help"
              @click="openApproveHistory(data.id, data.customer_name)"
              v-tooltip.top="'查看审批记录'"
              class="mr-2"
              style="border-radius: 8px; transition: all 0.2s ease"
            />
          </template>
        </Column>
      </DataTable>
    </div>
  </div>

  <!-- 新建/编辑客户抽屉 -->
  <Drawer
    v-model:visible="customerDrawerVisible"
    position="right"
    :style="{ width: '80rem' }"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :header="customerForm.id ? '编辑客户信息' : '新建客户信息'"
    class="p-fluid customer-drawer"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="customer_num">客户编码</label>
                <InputText
                  v-model="customerForm.customer_num"
                  class="field-input"
                  :disabled="true"
                />
              </div>
              <div class="field">
                <label for="customer_name" class="required">客户全称</label>
                <InputText
                  v-model="customerForm.customer_name"
                  :class="{ 'p-invalid': fieldErrors.customer_name }"
                />
                <Message
                  v-if="fieldErrors.customer_name"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.customer_name }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_name_intl">英文名称</label>
                <InputText v-model="customerForm.customer_name_intl" />
              </div>
              <div class="field">
                <label for="like_key" class="required">客户简称</label>
                <InputText
                  v-model="customerForm.like_key"
                  :class="{ 'p-invalid': fieldErrors.like_key }"
                />
                <Message
                  v-if="fieldErrors.like_key"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.like_key }}
                </Message>
              </div>
              <div class="field">
                <label for="group_name" class="required">所属集团</label>
                <InputText
                  v-model="customerForm.group_name"
                  :class="{ 'p-invalid': fieldErrors.group_name }"
                />
                <Message
                  v-if="fieldErrors.group_name"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.group_name }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_usci" class="required"
                  >统一社会信用代码</label
                >
                <InputText
                  v-model="customerForm.customer_usci"
                  :class="{ 'p-invalid': fieldErrors.customer_usci }"
                />
                <Message
                  v-if="fieldErrors.customer_usci"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.customer_usci }}
                </Message>
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 地址信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">地址信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="country">国家</label>
                <Select
                  v-model="customerForm.country"
                  :options="countries"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择国家"
                  @change="handleCountryChange"
                />
              </div>
              <div class="field">
                <label for="province">省份</label>
                <Select
                  v-model="customerForm.province"
                  :options="provincesOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择省份"
                  @change="handleProvinceChange"
                  :disabled="!customerForm.country || !provincesOptions.length"
                />
              </div>
              <div class="field">
                <label for="city">城市</label>
                <Select
                  v-model="customerForm.city"
                  :options="citiesOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择城市"
                  :disabled="!customerForm.province"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 业务信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">业务信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="sale_name" class="required">执行销售</label>
                <Select
                  v-model="customerForm.sale_name"
                  :options="salesOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择执行销售"
                  filter
                  :class="{ 'p-invalid': fieldErrors.sale_name }"
                />
                <Message
                  v-if="fieldErrors.sale_name"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.sale_name }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_class" class="required">客户类别</label>
                <Select
                  v-model="customerForm.customer_class"
                  :options="customerClassOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择客户类别"
                  :class="{ 'p-invalid': fieldErrors.customer_class }"
                />
                <Message
                  v-if="fieldErrors.customer_class"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.customer_class }}
                </Message>
              </div>
              <div class="field">
                <label for="customer_type" class="required">客户类型</label>
                <Select
                  v-model="customerForm.customer_type"
                  :options="customerTypeOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择客户类型"
                  :class="{ 'p-invalid': fieldErrors.customer_type }"
                />
                <Message
                  v-if="fieldErrors.customer_type"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.customer_type }}
                </Message>
              </div>
              <div class="field">
                <label for="trade_type" class="required">行业类型</label>
                <Select
                  v-model="customerForm.trade_type"
                  :options="tradeTypeOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择行业类型"
                  :class="{ 'p-invalid': fieldErrors.trade_type }"
                />
                <Message
                  v-if="fieldErrors.trade_type"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.trade_type }}
                </Message>
              </div>
              <div class="field">
                <label for="business" class="required">归属业务</label>
                <Select
                  v-model="customerForm.business"
                  :options="businessOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择归属业务"
                  :class="{ 'p-invalid': fieldErrors.business }"
                />
                <Message
                  v-if="fieldErrors.business"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.business }}
                </Message>
              </div>
              <div class="field">
                <label for="status" class="required">客户状态</label>
                <Select
                  v-model="customerForm.state"
                  :options="statusOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择客户状态"
                  :class="{ 'p-invalid': fieldErrors.state }"
                />
                <Message
                  v-if="fieldErrors.state"
                  severity="error"
                  variant="simple"
                  size="small"
                  class="mt-1"
                >
                  {{ fieldErrors.state }}
                </Message>
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 补充信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">补充信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="field">
                <label for="upload_file">上传附件</label>
                <FileUpload
                  ref="fileUpload"
                  mode="basic"
                  :auto="true"
                  :maxFileSize="1000000"
                  chooseLabel="选择文件"
                  severity="info"
                  class="p-button-outlined"
                />
              </div>
              <div class="field">
                <label for="remark">备注信息</label>
                <Textarea
                  v-model="customerForm.remark"
                  rows="3"
                  style="resize: none"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>
    </div>
    <template #footer>
      <Button
        label="取消"
        icon="pi pi-times"
        @click="customerDrawerVisible = false"
        class="p-button-text"
      />
      <Button label="保存" icon="pi pi-check" @click="saveCustomer" />
    </template>
  </Drawer>

  <!-- 联系人信息抽屉 -->
  <Drawer
    v-model:visible="contactDrawerVisible"
    position="right"
    :style="{ width: '80rem' }"
    :modal="true"
    :closable="true"
    :dismissable="false"
    :showCloseIcon="true"
    :header="contactMode === 'create' ? '新建客户联系人' : '编辑客户联系人'"
    class="p-fluid contact-drawer"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="name" class="required">联系人姓名</label>
                <InputText
                  v-model="contactForm.name"
                  :class="{ 'p-invalid': contactFieldErrors.name }"
                />
                <small v-if="contactFieldErrors.name" class="p-error">
                  {{ contactFieldErrors.name }}
                </small>
              </div>
              <div class="field">
                <label for="company">所属公司</label>
                <InputText v-model="contactForm.company" />
              </div>
              <div class="field">
                <label for="position">职位</label>
                <InputText v-model="contactForm.position" />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">联系方式</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="phone">电话</label>
                <InputText
                  v-model="contactForm.phone"
                  :class="{ 'p-invalid': contactFieldErrors.phone }"
                />
                <small v-if="contactFieldErrors.phone" class="p-error">
                  {{ contactFieldErrors.phone }}
                </small>
              </div>
              <div class="field">
                <label for="mobile_phone">手机</label>
                <InputText
                  v-model="contactForm.mobile_phone"
                  :class="{ 'p-invalid': contactFieldErrors.mobile_phone }"
                />
                <small v-if="contactFieldErrors.mobile_phone" class="p-error">
                  {{ contactFieldErrors.mobile_phone }}
                </small>
              </div>
              <div class="field">
                <label for="email">邮箱</label>
                <InputText
                  v-model="contactForm.email"
                  :class="{ 'p-invalid': contactFieldErrors.email }"
                />
                <small v-if="contactFieldErrors.email" class="p-error">
                  {{ contactFieldErrors.email }}
                </small>
              </div>
              <div class="field">
                <label for="address">地址</label>
                <InputText v-model="contactForm.address" />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 分类信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">分类信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="contact_class" class="required">联系人类别</label>
                <Select
                  v-model="contactForm.contact_class"
                  :options="contactClassOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择联系人类别"
                  :class="{ 'p-invalid': contactFieldErrors.contact_class }"
                />
                <small v-if="contactFieldErrors.contact_class" class="p-error">
                  {{ contactFieldErrors.contact_class }}
                </small>
              </div>
              <div class="field">
                <label for="contact_type" class="required">联系人类型</label>
                <Select
                  v-model="contactForm.contact_type"
                  :options="contactTypeOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择联系人类型"
                  :class="{ 'p-invalid': contactFieldErrors.contact_type }"
                />
                <small v-if="contactFieldErrors.contact_type" class="p-error">
                  {{ contactFieldErrors.contact_type }}
                </small>
              </div>
              <div class="field">
                <label for="own_type" class="required">归属类型</label>
                <Select
                  v-model="contactForm.own_type"
                  :options="ownTypeOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="选择归属类型"
                  :class="{ 'p-invalid': contactFieldErrors.own_type }"
                />
                <small v-if="contactFieldErrors.own_type" class="p-error">
                  {{ contactFieldErrors.own_type }}
                </small>
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 补充信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">补充信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="remark">备注</label>
                <Textarea
                  v-model="contactForm.remark"
                  rows="3"
                  style="resize: none"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>
    </div>
    <template #footer>
      <Button
        label="关闭"
        icon="pi pi-times"
        @click="contactDrawerVisible = false"
        class="p-button-text"
      />
      <Button label="保存" icon="pi pi-check" @click="saveContactInfo" />
    </template>
  </Drawer>

  <!-- 审批历史记录抽屉 -->
  <ApproveHistory
    v-if="selectedCustomerForHistory"
    v-model:visible="approveHistoryVisible"
    :customerId="selectedCustomerForHistory"
    :customerName="selectedCustomerForHistoryName || ''"
    @close="closeApproveHistory"
  />

  <!-- 客户详情弹框 -->
  <CustomerDetail
    v-model:visible="customerDetailVisible"
    :customer="selectedCustomerForDetail"
    @update:visible="customerDetailVisible = $event"
  />
</template>

<style scoped>
.customer-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

:deep(.p-datatable-table .p-datatable-thead > tr > th) {
  background: #f1f5f9 !important;
  color: #475569 !important;
  font-weight: 600 !important;
  border-color: #e2e8f0 !important;
}

:deep(.p-datatable-table .p-datatable-tbody > tr:nth-child(odd)) {
  background: #f8fafc !important;
}

:deep(.p-datatable-table .p-datatable-tbody > tr:hover) {
  background: #e0f2fe !important;
}

:deep(
    .p-datatable-table .p-datatable-tbody > tr.p-datatable-row-expansion:hover
  ) {
  background: #ffffff !important;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

:deep(.p-button) {
  transition: all 0.2s ease;
}

:deep(.p-button:active) {
  transform: scale(0.98);
}

:deep(.p-inputtext:focus) {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.25);
  border-color: #007aff;
}

:deep(.p-button .p-button-info) {
  background: #007aff;
  border-color: #007aff;
}

:deep(.p-button.p-button-info:hover) {
  background: #0066d6;
  border-color: #0066d6;
}

:deep(.p-button .p-button-warn) {
  background: #ff9500;
  border-color: #ff9500;
}

:deep(.p-button.p-button-warn:hover) {
  background: #e68600;
  border-color: #e68600;
}

/* 客户抽屉样式 */
:deep(.customer-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

.form-section {
  margin-bottom: 2rem;
  background: var(--surface-card);
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--surface-card);
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.025em;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field-input:disabled {
  background-color: var(--p-surface-100);
  color: var(--p-text-color-secondary);
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .section-header,
  .section-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
