<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import {
  createDepartment,
  getDepartments,
  updateDepartment,
} from "../../services/department";
import {
  createUser,
  getDepartmentUsers,
  updateUser,
  UpdateUserRequest,
} from "../../services/user";
import { getRoleSimple } from "../../services/role";
import type { Department } from "../../types/department";
import type { User } from "../../types/user";
import type { RoleSimple } from "../../services/role";
import { userStatusMap, userStatusSeverityMap } from "../../utils/const";
import { Fluid } from "primevue";

const departments = ref<Department[]>([]);
const selectedDepartment = ref<{ key: string; label: string } | null>(null);
const users = ref<User[]>([]);
const loading = ref(false);
const userLoading = ref(false);
const toast = useToast();
const treeNodes = ref<{ key: string; label: string; children?: any[] }[]>([]);
const selectedKey = ref<{ [key: string]: boolean }>({});
const roles = ref<RoleSimple[]>([]);

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});
const totalRecords = ref(0);

// 用户筛选参数
const userFilterValue = ref("");

// 加载部门数据
const loadDepartments = async () => {
  try {
    loading.value = true;
    const response = await getDepartments();
    departments.value = response.data;
    treeNodes.value = convertToTreeNodes(response.data);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载部门数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 加载用户数据
const loadUsers = async (departmentId: number) => {
  try {
    userLoading.value = true;
    const params: any = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    // 如果有用户筛选值，传递word参数
    if (userFilterValue.value.trim()) {
      params.word = userFilterValue.value.trim();
    }

    const response = await getDepartmentUsers(departmentId, params);
    users.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载用户数据失败",
      life: 3000,
    });
  } finally {
    userLoading.value = false;
  }
};

// 处理部门选择
const onNodeSelect = (node: { key: string; label: string }) => {
  selectedDepartment.value = node;
  selectedKey.value = { [node.key]: true };
  lazyParams.value.page = 1;
  loadUsers(parseInt(node.key));
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  if (selectedDepartment.value) {
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 转换函数
const convertToTreeNodes = (
  data: Department[]
): { key: string; label: string; children?: any[] }[] => {
  if (!data || data.length === 0) {
    return [];
  }

  return data.map((node) => {
    const treeNode = {
      key: node.id.toString(),
      label: node.department_name,
      originalData: node,
    };

    if (node.children && node.children.length > 0) {
      Object.assign(treeNode, { children: convertToTreeNodes(node.children) });
    }

    return treeNode;
  });
};

// 加载角色数据
const loadRoles = async () => {
  try {
    const response = await getRoleSimple();
    roles.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载角色数据失败",
      life: 3000,
    });
  }
};

// 处理用户筛选
const handleUserFilter = () => {
  if (selectedDepartment.value) {
    lazyParams.value.page = 1;
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 重置用户筛选
const resetUserFilter = () => {
  userFilterValue.value = "";
  if (selectedDepartment.value) {
    lazyParams.value.page = 1;
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 根据角色ID获取角色名称
const getRoleNamesByIds = (roleIds: number[]): string[] => {
  if (!roleIds || roleIds.length === 0) {
    return [];
  }
  return roleIds.map((id) => {
    const role = roles.value.find((r) => r.id === id);
    return role ? role.role_name : `未知角色(${id})`;
  });
};

onMounted(() => {
  loadDepartments();
  loadRoles();
});

// 新增用户对话框控制
const userDrawer = ref(false);
const editMode = ref(false);
const userForm = ref<{
  id?: number;
  username: string;
  password: string;
  email: string;
  mobile: string;
  role_ids: number[];
}>({
  username: "",
  password: "",
  email: "",
  mobile: "",
  role_ids: [],
});
const submitted = ref(false);

// 打开新增用户对话框
const openNew = () => {
  // 检查是否选择了部门
  if (!selectedDepartment.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择一个部门",
      life: 3000,
    });
    return;
  }

  userForm.value = {
    username: "",
    password: "",
    email: "",
    mobile: "",
    role_ids: [],
  };
  editMode.value = false;
  submitted.value = false;
  userDrawer.value = true;
  fieldErrors.value = {};
};

// 打开编辑用户对话框
const openEdit = (user: User) => {
  // 检查是否选择了部门
  if (!selectedDepartment.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择一个部门",
      life: 3000,
    });
    return;
  }

  userForm.value = {
    id: user.id,
    username: user.username,
    password: "", // 编辑时不显示密码
    email: user.email,
    mobile: user.mobile,
    role_ids: user.role_ids,
  };
  editMode.value = true;
  submitted.value = false;
  userDrawer.value = true;
  fieldErrors.value = {};
};

// 隐藏对话框
const hideDialog = () => {
  userDrawer.value = false;
  submitted.value = false;
  editMode.value = false;
};

// 新增/修改部门相关状态
const departmentForm = ref<{
  id?: number;
  department_name: string;
  parent_id?: number | null;
}>({
  department_name: "",
  parent_id: null,
});
const departmentDrawerVisible = ref(false);
const fieldErrors = ref<{ [key: string]: string }>({});

// 打开添加部门抽屉
const openAddDepartment = (node?: any) => {
  departmentForm.value = {
    department_name: "",
    parent_id: node ? parseInt(node.key) : null,
  };
  fieldErrors.value = {};
  departmentDrawerVisible.value = true;
};

// 打开编辑部门抽屉
const openEditDepartment = (node?: any) => {
  departmentForm.value = {
    id: parseInt(node.key),
    department_name: node.label,
  };
  fieldErrors.value = {};
  departmentDrawerVisible.value = true;
};

// 提交新建部门
const saveDepartment = async () => {
  try {
    let response;
    if (departmentForm.value.id) {
      // 编辑角色时调用updateRole方法
      response = await updateDepartment(
        departmentForm.value,
        departmentForm.value.id
      );
    } else {
      // 新增角色时调用createRole方法
      response = await createDepartment(departmentForm.value);
    }
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: departmentForm.value.id ? "部门更新成功" : "部门创建成功",
        life: 3000,
      });
    }
    departmentDrawerVisible.value = false;
    loadDepartments(); // 刷新部门树
  } catch (error: any) {
    if (error.response.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: departmentForm.value.id ? "部门更新失败" : "部门创建失败",
        life: 3000,
      });
    }
  }
};

// user表单验证
const validateUserForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "username", label: "用户名" },
    { key: "email", label: "邮箱" },
    { key: "mobile", label: "手机号" },
  ];

  requiredFields.forEach((field) => {
    const value = userForm.value[field.key as keyof typeof userForm.value];
    if (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "") ||
      (typeof value === "number" && isNaN(value))
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  // 用户必须至少选择一个角色
  if (!userForm.value.role_ids || userForm.value.role_ids.length === 0) {
    fieldErrors.value.role_ids = "请至少选择一个角色";
    isValid = false;
  }

  // password的校验只在新增用户时进行
  if (!editMode.value) {
    if (!userForm.value.password) {
      fieldErrors.value.password = "密码不能为空";
      isValid = false;
    }
  }
  return isValid;
};

// 保存用户
const saveUser = async () => {
  submitted.value = true;

  // 前端表单验证
  if (!validateUserForm()) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
    return;
  }

  const departmentId = selectedDepartment.value
    ? selectedDepartment.value.key
    : "";

  try {
    let response;
    if (userForm.value.id) {
      // 编辑角色时调用updateRole方法
      const updateData: UpdateUserRequest = {
        username: userForm.value.username,
        email: userForm.value.email,
        mobile: userForm.value.mobile,
        role_ids: userForm.value.role_ids,
      };
      response = await updateUser(
        userForm.value.id,
        parseInt(departmentId),
        updateData
      );
    } else {
      // 新增角色时调用createRole方法
      response = await createUser(userForm.value, parseInt(departmentId));
    }
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: userForm.value.id ? "用户更新成功" : "用户创建成功",
        life: 3000,
      });
    }
    userDrawer.value = false;
    // 重新加载用户列表
    if (selectedDepartment.value) {
      loadUsers(parseInt(selectedDepartment.value.key));
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: editMode.value ? "更新用户失败" : "创建用户失败",
      life: 3000,
    });
  }
};
</script>

<template>
  <div class="department-list-container">
    <Toast />
    <!-- 使用Splitter组件分栏显示 -->
    <Splitter style="height: calc(100vh - 9rem)">
      <!-- 左侧：部门树 (20%) -->
      <SplitterPanel :size="20" :minSize="20" class="department-panel">
        <div class="card h-full">
          <!-- 部门筛选区域 -->
          <Toolbar class="mb-2">
            <template #start>
              <Message icon="pi pi-sitemap" severity="success">部门</Message>
            </template>
            <template #end>
              <Button
                label="新增"
                icon="pi pi-plus"
                @click="openAddDepartment"
              />
            </template>
          </Toolbar>
          <div class="tree-container">
            <Tree
              v-model:selectionKeys="selectedKey"
              :value="treeNodes"
              :loading="loading"
              @nodeSelect="onNodeSelect"
              selectionMode="single"
              class="department-tree"
            >
              <template #default="slotProps">
                <div
                  class="tree-node-content"
                  :class="{ selected: selectedKey[slotProps.node.key] }"
                >
                  <div class="node-label">
                    <span class="node-text">{{ slotProps.node.label }}</span>
                  </div>
                  <div class="node-actions">
                    <Button
                      icon="pi pi-plus"
                      class="node-action-btn add-btn"
                      @click.stop="openAddDepartment(slotProps.node)"
                      v-tooltip.top="'添加子部门'"
                      severity="info"
                    />
                    <Button
                      icon="pi pi-pencil"
                      class="node-action-btn edit-btn"
                      @click.stop="openEditDepartment(slotProps.node)"
                      v-tooltip.top="'编辑部门'"
                    />
                  </div>
                </div>
              </template>
            </Tree>
          </div>
        </div>
      </SplitterPanel>
      <!-- 右侧：用户列表 (80%) -->
      <SplitterPanel :size="80" :minSize="70" class="user-panel">
        <div class="card h-full">
          <!-- 用户筛选区域 -->
          <Toolbar class="mb-2">
            <template #start>
              <Message icon="pi pi-users" severity="success">用户列表</Message>
            </template>
            <template #end>
              <div class="flex flex-nowrap align-items-center gap-2">
                <FloatLabel>
                  <label>用户名称</label>
                  <InputText v-model="userFilterValue" class="flex-grow-1" />
                </FloatLabel>
                <Button
                  icon="pi pi-search"
                  @click="handleUserFilter"
                  rounded
                  class="flex-shrink-0"
                />
                <Button
                  icon="pi pi-refresh"
                  @click="resetUserFilter"
                  outlined
                  rounded
                  class="flex-shrink-0"
                  severity="secondary"
                />
              </div>
              <Divider layout="vertical" />
              <Button
                label="新增"
                outlined
                icon="pi pi-plus"
                @click="openNew"
                :disabled="!selectedDepartment"
              />
            </template>
          </Toolbar>
          <div v-if="!selectedDepartment" class="empty-message">
            <i
              class="pi pi-info-circle"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>请选择左侧部门查看用户列表</p>
          </div>
          <DataTable
            v-else
            :value="users"
            :lazy="true"
            :paginator="true"
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            :totalRecords="totalRecords"
            :loading="userLoading"
            @page="onPage($event)"
            showGridlines
            scrollable
            scrollHeight="calc(100vh - 21rem)"
            class="p-datatable-sm"
          >
            <template #empty>
              <div class="empty-message">
                <i
                  class="pi pi-inbox"
                  style="
                    font-size: 2rem;
                    color: var(--p-text-color-secondary);
                    margin-bottom: 1rem;
                  "
                ></i>
                <p>暂无用户数据</p>
              </div>
            </template>
            <Column field="username" header="用户名">
              <template #body="{ data }">
                <span class="font-medium">{{ data.username }}</span>
              </template>
            </Column>
            <Column field="email" header="邮箱" />
            <Column field="mobile" header="手机号" />
            <Column field="roles" header="所属角色">
              <template #body="slotProps">
                <div
                  v-if="
                    slotProps.data.role_ids &&
                    slotProps.data.role_ids.length > 0
                  "
                >
                  <Tag
                    v-for="(roleName, index) in getRoleNamesByIds(
                      slotProps.data.role_ids
                    )"
                    :key="index"
                    :value="roleName"
                    severity="contrast"
                    class="mr-1"
                  />
                </div>
                <span v-else class="text-muted">暂无角色</span>
              </template>
            </Column>
            <Column field="state" header="状态">
              <template #body="slotProps">
                <Tag
                  :severity="
                    userStatusSeverityMap[slotProps.data.state] || 'info'
                  "
                  :value="userStatusMap[slotProps.data.state]"
                />
              </template>
            </Column>
            <Column header="操作" :exportable="false" style="min-width: 8rem">
              <template #body="slotProps">
                <Button
                  icon="pi pi-pencil"
                  outlined
                  rounded
                  class="mr-2"
                  @click="openEdit(slotProps.data)"
                  :disabled="!selectedDepartment"
                />
              </template>
            </Column>
          </DataTable>
        </div>
      </SplitterPanel>
    </Splitter>
    <!-- 新增/编辑用户抽屉 -->
    <Drawer
      v-model:visible="userDrawer"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="false"
      :showCloseIcon="true"
      :header="editMode ? '编辑用户' : '新增用户'"
      class="user-drawer p-fluid"
    >
      <div class="p-4">
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-2 gap-3">
                <div class="field">
                  <label for="username" class="required">用户名</label>
                  <InputText
                    id="username"
                    v-model="userForm.username"
                    :class="[{ 'p-invalid': fieldErrors.username }]"
                    placeholder="请输入用户名"
                  />
                  <Message
                    v-if="fieldErrors.username"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ fieldErrors.username }}
                  </Message>
                </div>
                <div class="field" v-if="!editMode">
                  <label for="password" :class="{ required: !editMode }"
                    >密码</label
                  >
                  <InputText
                    id="password"
                    v-model="userForm.password"
                    :required="!editMode"
                    :disabled="editMode"
                    :class="{
                      'p-invalid': fieldErrors.password && !editMode,
                    }"
                    placeholder="请输入密码"
                  />
                  <Message
                    v-if="fieldErrors.password && !editMode"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ fieldErrors.password }}
                  </Message>
                </div>
                <div class="field">
                  <label for="email" class="required">邮箱</label>
                  <InputText
                    id="email"
                    v-model="userForm.email"
                    :class="[{ 'p-invalid': fieldErrors.email }]"
                    placeholder="请输入邮箱地址"
                  />
                  <Message
                    v-if="fieldErrors.email"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ fieldErrors.email }}
                  </Message>
                </div>
                <div class="field">
                  <label for="mobile" class="required">手机号</label>
                  <InputText
                    id="mobile"
                    v-model="userForm.mobile"
                    :class="[{ 'p-invalid': fieldErrors.mobile }]"
                    placeholder="请输入手机号码"
                  />
                  <Message
                    v-if="fieldErrors.mobile"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ fieldErrors.mobile }}
                  </Message>
                </div>
              </div>
            </Fluid>
          </div>
        </div>
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">归属信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-1 gap-4">
                <div class="field">
                  <label for="roles" class="required">角色</label>
                  <MultiSelect
                    id="roles"
                    v-model="userForm.role_ids"
                    filter
                    fluid
                    display="chip"
                    :options="roles"
                    optionLabel="role_name"
                    optionValue="id"
                    placeholder="请选择角色"
                    :class="[{ 'p-invalid': fieldErrors.role_ids }]"
                  />
                  <Message
                    v-if="fieldErrors.role_ids"
                    severity="error"
                    variant="simple"
                    size="small"
                    class="mt-1"
                  >
                    {{ fieldErrors.role_ids }}
                  </Message>
                </div>
                <div class="field">
                  <label for="department_display" class="required"
                    >所属部门</label
                  >
                  <InputText
                    id="department_display"
                    :value="selectedDepartment ? selectedDepartment.label : ''"
                    disabled
                    fluid
                    placeholder="当前选中的部门"
                  />
                </div>
              </div>
            </Fluid>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            text
            @click="hideDialog"
            class="mr-2"
          />
          <Button label="保存" icon="pi pi-check" @click="saveUser" />
        </div>
      </template>
    </Drawer>
    <!-- 新增/编辑部门抽屉 -->
    <Drawer
      v-model:visible="departmentDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="false"
      :showCloseIcon="true"
      :header="departmentForm.id ? '编辑部门' : '新建部门'"
      class="department-drawer"
    >
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-1 gap-4">
              <div class="field">
                <label for="departmentName" class="required"> 部门名称 </label>
                <InputText
                  id="departmentName"
                  v-model="departmentForm.department_name"
                  :class="[{ 'p-invalid': fieldErrors.department_name }]"
                  placeholder="请输入部门名称"
                />
                <small v-if="fieldErrors.department_name" class="p-error">
                  {{ fieldErrors.department_name }}
                </small>
              </div>
            </div>
          </Fluid>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            text
            @click="departmentDrawerVisible = false"
            class="mr-2"
          />
          <Button label="保存" icon="pi pi-check" @click="saveDepartment" />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.department-list-container {
  padding: 1rem;
  background: #f8f9fa;
}

/* Splitter样式 */
:deep(.p-splitter) {
  border: none;
  background: transparent;
}

:deep(.p-splitter-panel) {
  background: transparent;
}

:deep(.p-splitter-gutter) {
  background: var(--p-surface-border);
  transition: background-color 0.2s;
}

:deep(.p-splitter-gutter:hover) {
  background: var(--p-primary-500);
}

:deep(.p-splitter-gutter-handle) {
  background: transparent;
}

/* DataTable样式 */
:deep(.p-datatable-table .p-datatable-thead > tr > th) {
  background: #f1f5f9 !important;
  color: #475569 !important;
  font-weight: 600 !important;
  border-color: #e2e8f0 !important;
}

:deep(.p-datatable-table .p-datatable-tbody > tr:nth-child(odd)) {
  background: #f8fafc !important;
}

:deep(.p-datatable-table .p-datatable-tbody > tr:hover) {
  background: #e0f2fe !important;
}

/* 部门面板样式 - 苹果风格 */
.department-panel {
  background: #f8f9fa;
}

.department-panel .card {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 树节点样式 */
.department-tree {
  background: transparent;
  border: none;
  padding: 0;
}

/* 隐藏默认的树线和按钮 */
:deep(.department-tree .p-tree-container) {
  background: transparent;
  border: none;
  padding: 0;
}

:deep(.department-tree .p-treenode) {
  padding: 0;
  margin: 0;
}

:deep(.department-tree .p-treenode-content) {
  border: none;
  padding: 0;
  margin: 2px 12px;
  border-radius: 8px;
  background: transparent;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.department-tree .p-treenode-content:hover) {
  background: rgba(0, 122, 255, 0.08);
}

:deep(.department-tree .p-treenode-content.p-treenode-selectable.p-highlight) {
  background: rgba(0, 122, 255, 0.12);
  box-shadow: 0 0 0 1px rgba(0, 122, 255, 0.2);
}

/* 自定义节点内容 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 1px;
  border-radius: 8px;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tree-node-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 122, 255, 0.02) 50%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.15s ease;
}

.tree-node-content:hover::before {
  opacity: 1;
}

.node-label {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.node-icon {
  font-size: 14px;
  color: #007aff;
  flex-shrink: 0;
}

.node-text {
  font-size: 15px;
  font-weight: 400;
  color: #1d1d1f;
  letter-spacing: -0.2px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-node-content.selected .node-text {
  font-weight: 500;
  color: #007aff;
}

/* 节点操作按钮 */
.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.15s ease;
  flex-shrink: 0;
  margin-left: 1rem;
}

.tree-node-content:hover .node-actions {
  opacity: 1;
}

.tree-node-content.selected .node-actions {
  opacity: 1;
}

.node-action-btn {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: #86868b;
  font-size: 11px;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-action-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  color: #1d1d1f;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.node-action-btn:active {
  transform: scale(0.95);
}

.edit-btn:hover {
  color: #ff9500;
}

.add-btn:hover {
  color: #007aff;
}

/* 隐藏默认的树展开/折叠按钮 */
:deep(.department-tree .p-tree-toggler) {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background: rgba(0, 122, 255, 0.1);
  border: none;
  color: #007aff;
  margin-right: 8px;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.department-tree .p-tree-toggler:hover) {
  background: rgba(0, 122, 255, 0.2);
  transform: scale(1.1);
}

:deep(.department-tree .p-tree-toggler:focus) {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3);
  outline: none;
}

/* 树线样式 */
:deep(.department-tree .p-treenode-children) {
  padding-left: 20px;
  border-left: 1px solid rgba(0, 122, 255, 0.1);
  margin-left: 10px;
}

/* 加载状态 */
:deep(.department-tree .p-tree-loading-overlay) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 12px;
}

:deep(.department-tree .p-tree-loading-icon) {
  color: #007aff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-node-content {
    padding: 8px 10px;
  }

  .node-text {
    font-size: 14px;
  }

  .node-action-btn {
    width: 22px;
    height: 22px;
    font-size: 10px;
  }
}

/* 无障碍支持 */
.node-action-btn:focus {
  outline: 2px solid #007aff;
  outline-offset: 2px;
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .department-panel {
    background: #1c1c1e;
  }

  .department-panel .card {
    background: #2c2c2e;
    border-color: rgba(255, 255, 255, 0.1);
  }

  .node-text {
    color: #ffffff;
  }

  .tree-node-content.selected .node-text {
    color: #0a84ff;
  }

  .node-action-btn {
    background: rgba(0, 0, 0, 0.3);
    color: #8e8e93;
  }

  .node-action-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    color: #ffffff;
  }
}

/* 用户面板样式 */
.user-panel .empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
  height: 300px;
}

.user-panel .empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

.p-fluid .p-button {
  width: auto;
}

.p-button-sm {
  padding: 0.25rem;
}

/* 抽屉样式 */
:deep(.user-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* 表单分组样式 */
.form-section {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 12px;
  margin-bottom: 1.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-header {
  background: var(--surface-ground);
  padding: 1rem 1.5rem 0.5rem;
}

.section-title {
  margin: 0 0 0.75rem 0;
  color: var(--text-color);
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-section {
    margin-bottom: 1rem;
  }

  .section-content {
    padding: 1rem;
  }
}

/* MultiSelect 芯片样式 */
:deep(.p-multiselect .p-multiselect-token) {
  background: var(--primary-50);
  color: var(--primary-700);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  margin: 0.125rem;
}

/* TreeSelect 样式 */
:deep(.p-treeselect .p-treeselect-label) {
  padding: 0.75rem;
}
</style>
