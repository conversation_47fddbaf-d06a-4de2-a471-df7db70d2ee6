<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { getOperationLogList } from "../services/operationLog";
import type {
  OperationLogItem,
  OperationType,
  ModuleName,
  FieldChange,
} from "../types/operationLog";
import {
  OperationTypeMap,
  ModuleNameMap,
  OperationTypeIconMap,
  OperationTypeColorMap,
  ModuleIconMap,
} from "../types/operationLog";
import { formatDateTime } from "../utils/common";

// 数据状态
const loading = ref(true);
const operationLogs = ref<OperationLogItem[]>([]);
const totalLogs = ref(0);

// 筛选状态
const filters = ref({
  operation_type: undefined as OperationType | undefined,
  table_name: undefined as ModuleName | undefined,
  page: 1,
  pageSize: 10,
});

// 操作类型选项
const operationTypeOptions = ref([
  { label: "全部", value: undefined },
  { label: "新建", value: "create" as OperationType },
  { label: "编辑", value: "update" as OperationType },
]);

// 模块名称选项
const moduleNameOptions = ref([
  { label: "全部", value: undefined },
  { label: "客户", value: "customer" as ModuleName },
  { label: "客户联系人", value: "contact" as ModuleName },
  { label: "合同", value: "contract" as ModuleName },
  { label: "订单", value: "order" as ModuleName },
  { label: "用户", value: "user" as ModuleName },
  { label: "角色", value: "role" as ModuleName },
]);

// 格式化时间线数据
const timelineEvents = computed(() => {
  return operationLogs.value.map((log) => ({
    id: log.id,
    operator: log.operator,
    operationType: log.operation_type,
    operationTypeLabel: OperationTypeMap[log.operation_type],
    operationTypeIcon: OperationTypeIconMap[log.operation_type],
    operationTypeColor: OperationTypeColorMap[log.operation_type],
    moduleName: log.table_name,
    moduleLabel: ModuleNameMap[log.table_name],
    moduleIcon: ModuleIconMap[log.table_name],
    tableId: log.table_id,
    content: log.operation_content,
    createdAt: log.created_at,
    formattedTime: formatDateTime(log.created_at),
  }));
});

// 格式化变更内容
type FormattedChange = {
  fieldName: string;
  oldValue: string;
  newValue: string;
};

const formatChangeContent = (
  content: string | FieldChange[]
): string | FormattedChange[] => {
  if (typeof content === "string") {
    return content;
  }

  if (Array.isArray(content)) {
    return content.map((change) => ({
      fieldName: change.field_name,
      oldValue: change.old_value,
      newValue: change.new_value,
    }));
  }

  return "";
};

// 类型守卫函数
const isFormattedChangeArray = (
  content: string | FormattedChange[]
): content is FormattedChange[] => {
  return Array.isArray(content);
};

// 加载操作记录数据
const loadOperationLogs = async () => {
  loading.value = true;
  try {
    const response = await getOperationLogList(filters.value);
    if (response.code === 200) {
      operationLogs.value = response.data.records;
      totalLogs.value = response.data.page.total;
    }
  } catch (error) {
    console.error("Failed to load operation logs:", error);
  } finally {
    loading.value = false;
  }
};

// 重置筛选
const resetFilters = () => {
  filters.value = {
    operation_type: undefined,
    table_name: undefined,
    page: 1,
    pageSize: 10,
  };
};

// 监听筛选变化
watch(
  () => [filters.value.operation_type, filters.value.table_name],
  () => {
    filters.value.page = 1;
    loadOperationLogs();
  },
  { deep: true }
);

// 分页变化
const onPageChange = (event: any) => {
  filters.value.page = event.page + 1;
  filters.value.pageSize = event.rows;
  loadOperationLogs();
};

onMounted(() => {
  loadOperationLogs();
});
</script>

<template>
  <div class="home-container">
    <div class="card">
      <!-- 筛选区域 -->
      <Toolbar class="mb-2">
        <template #end>
          <div class="flex gap-2">
            <FloatLabel class="filter-item">
              <Select
                id="operation_type"
                v-model="filters.operation_type"
                :options="operationTypeOptions"
                optionLabel="label"
                optionValue="value"
                showClear
                style="width: 12rem"
              />
              <label for="operation_type">操作类型</label>
            </FloatLabel>

            <FloatLabel class="filter-item">
              <Select
                id="table_name"
                v-model="filters.table_name"
                :options="moduleNameOptions"
                optionLabel="label"
                optionValue="value"
                showClear
                style="width: 12rem"
              />
              <label for="table_name">模块名称</label>
            </FloatLabel>
            <Button
              icon="pi pi-search"
              rounded
              @click="loadOperationLogs"
              :loading="loading"
            />
            <Button
              icon="pi pi-refresh"
              @click="resetFilters"
              rounded
              outlined
              severity="success"
            />
          </div>
        </template>
      </Toolbar>

      <!-- 时间线主体 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner strokeWidth="3" />
        <p>正在加载操作记录...</p>
      </div>

      <div v-else-if="timelineEvents.length === 0" class="empty-container">
        <div class="empty-message">
          <i
            class="pi pi-inbox"
            style="
              font-size: 2rem;
              color: var(--p-text-color-secondary);
              margin-bottom: 1rem;
            "
          ></i>
          <p>暂无操作记录</p>
        </div>
      </div>

      <div v-else class="timeline-container">
        <Timeline
          :value="timelineEvents"
          class="operation-timeline"
          align="alternate"
        >
          <template #marker="{ item }">
            <div
              class="timeline-marker"
              :class="`marker-${item.operationTypeColor}`"
            >
              <i :class="item.operationTypeIcon" />
            </div>
          </template>

          <template #content="{ item }">
            <div class="timeline-event">
              <!-- 事件头部 -->
              <div class="event-header">
                <div class="event-title">
                  <i :class="item.moduleIcon" class="module-icon" />
                  <span class="event-action">
                    {{ item.operationTypeLabel }}{{ item.moduleLabel }}
                  </span>
                  <Tag
                    :value="item.operationTypeLabel"
                    :severity="item.operationTypeColor"
                    class="action-tag"
                  />
                </div>
                <div class="event-meta">
                  <span class="event-time">{{ item.formattedTime }}</span>
                  <span class="event-operator">{{ item.operator }}</span>
                </div>
              </div>

              <!-- 事件内容 -->
              <div class="event-content">
                <div
                  v-if="item.operationType === 'create'"
                  class="create-content"
                >
                  <p class="content-text">
                    创建了新的{{ item.moduleLabel }}记录 (ID:
                    {{ item.tableId }})
                  </p>
                  <!-- 显示新建操作的详细内容 -->
                  <div
                    v-if="
                      typeof item.content === 'string' && item.content.trim()
                    "
                    class="create-details"
                  >
                    <div class="details-header">
                      <i class="pi pi-info-circle" />
                      <span>操作详情</span>
                    </div>
                    <div class="details-content">
                      {{ item.content }}
                    </div>
                  </div>
                </div>

                <div
                  v-else-if="item.operationType === 'update'"
                  class="update-content"
                >
                  <p class="content-text">
                    编辑了{{ item.moduleLabel }}记录 (ID: {{ item.tableId }})
                  </p>

                  <!-- 显示字符串类型的更新内容 -->
                  <div
                    v-if="
                      typeof formatChangeContent(item.content) === 'string' &&
                      formatChangeContent(item.content)
                    "
                    class="update-details"
                  >
                    <div class="details-header">
                      <i class="pi pi-info-circle" />
                      <span>操作详情</span>
                    </div>
                    <div class="details-content">
                      {{ formatChangeContent(item.content) }}
                    </div>
                  </div>

                  <!-- 显示数组类型的变更详情 -->
                  <div
                    v-if="
                      isFormattedChangeArray(formatChangeContent(item.content))
                    "
                    class="changes-list"
                  >
                    <div class="changes-header">
                      <i class="pi pi-list" />
                      <span>变更详情</span>
                    </div>
                    <div
                      v-for="change in formatChangeContent(item.content) as FormattedChange[]"
                      :key="change.fieldName"
                      class="change-item"
                    >
                      <span class="field-name">{{ change.fieldName }}:</span>
                      <span class="old-value">{{
                        change.oldValue || "(空)"
                      }}</span>
                      <i class="pi pi-arrow-right change-arrow" />
                      <span class="new-value">{{
                        change.newValue || "(空)"
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </Timeline>
      </div>

      <!-- 分页器 -->
      <div
        v-if="!loading && timelineEvents.length > 0"
        class="pagination-container"
      >
        <Paginator
          :first="(filters.page - 1) * filters.pageSize"
          :rows="filters.pageSize"
          :totalRecords="totalLogs"
          :rowsPerPageOptions="[5, 10, 20, 50]"
          @page="onPageChange"
          template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
          class="timeline-paginator"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  min-height: calc(100vh - 10rem);
  padding: 1rem;
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-container p,
.empty-container p {
  color: #86868b;
  font-size: 1.1rem;
  margin-top: 1rem;
}

.empty-container h3 {
  color: #1d1d1f;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
}

/* 时间线容器 */
.timeline-container {
  padding: 1rem 0;
  max-height: calc(100vh - 23rem);
  overflow-y: auto;
  overflow-x: hidden;
}

/* PrimeVue Timeline 样式覆盖 */
.operation-timeline :deep(.p-timeline-event-connector) {
  background-color: #e5e5ea;
  width: 2px;
}

.operation-timeline :deep(.p-timeline-event-marker) {
  border: none;
  padding: 0;
  background: transparent;
}

/* 时间线标记 */
.timeline-marker {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.timeline-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.marker-success {
  background: linear-gradient(135deg, #34c759, #30d158);
}

.marker-info {
  background: linear-gradient(135deg, #007aff, #5856d6);
}

/* 时间线事件 */
.timeline-event {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-left: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.timeline-event:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 事件头部 */
.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.event-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.module-icon {
  font-size: 1.1rem;
  color: #007aff;
  min-width: 16px;
}

.event-action {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-right: 0.5rem;
}

.action-tag {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.event-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
  text-align: right;
}

.event-time {
  font-size: 0.9rem;
  color: #86868b;
  font-weight: 500;
}

.event-operator {
  font-size: 0.85rem;
  color: #6e6e73;
  font-weight: 400;
}

/* 事件内容 */
.event-content {
  margin-top: 0.75rem;
}

.content-text {
  color: #6e6e73;
  font-size: 0.95rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

/* 变更列表 */
.changes-list {
  background: #f9f9fb;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e5e5ea;
}

.changes-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1d1d1f;
}

.changes-header i {
  color: #007aff;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.change-item:last-child {
  margin-bottom: 0;
}

.field-name {
  font-size: 0.85rem;
  font-weight: 600;
  color: #1d1d1f;
  min-width: fit-content;
  flex-shrink: 0;
}

.old-value {
  color: #ff3b30;
  background: rgba(255, 59, 48, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: -apple-system-mono, Menlo, Monaco, "Courier New", monospace;
  word-break: break-all;
  font-size: 0.85rem;
}

.new-value {
  color: #34c759;
  background: rgba(52, 199, 89, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: -apple-system-mono, Menlo, Monaco, "Courier New", monospace;
  word-break: break-all;
  font-size: 0.85rem;
}

.change-arrow {
  color: #86868b;
  font-size: 0.8rem;
  flex-shrink: 0;
}

/* 操作详情样式 */
.create-details,
.update-details {
  background: #f0f8ff;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #d6e8ff;
  margin-top: 0.75rem;
}

.details-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1d1d1f;
}

.details-header i {
  color: #007aff;
}

.details-content {
  font-size: 0.9rem;
  color: #3c4043;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 分页器 */
.pagination-container {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e5ea;
}

.timeline-paginator :deep(.p-paginator) {
  background: transparent;
  border: none;
  padding: 0;
  justify-content: center;
}

.timeline-paginator :deep(.p-paginator-page),
.timeline-paginator :deep(.p-paginator-prev),
.timeline-paginator :deep(.p-paginator-next),
.timeline-paginator :deep(.p-paginator-first),
.timeline-paginator :deep(.p-paginator-last) {
  border-radius: 8px;
  border: 1px solid #d2d2d7;
  margin: 0 0.25rem;
  transition: all 0.2s ease;
}

.timeline-paginator :deep(.p-paginator-page.p-highlight) {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.timeline-paginator :deep(.p-paginator-page:hover),
.timeline-paginator :deep(.p-paginator-prev:hover),
.timeline-paginator :deep(.p-paginator-next:hover),
.timeline-paginator :deep(.p-paginator-first:hover),
.timeline-paginator :deep(.p-paginator-last:hover) {
  background: rgba(0, 122, 255, 0.1);
  border-color: #007aff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    padding: 1rem;
  }

  .event-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .event-meta {
    align-items: flex-start;
    text-align: left;
  }

  .timeline-event {
    margin-left: 0.5rem;
    padding: 1rem;
    margin-right: 0.5rem !important;
  }

  /* 移动端时取消alternate布局的特殊margin */
  .operation-timeline :deep(.p-timeline-event:nth-child(even) .timeline-event) {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }

  .change-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .change-arrow {
    transform: rotate(90deg);
  }
}

@media (max-width: 480px) {
  .home-container {
    padding: 0.75rem;
  }

  .timeline-marker {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .timeline-event {
    margin-left: 0.25rem;
    padding: 0.75rem;
  }

  .loading-container,
  .empty-container {
    padding: 2rem 1rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .home-container {
    background: #000000;
  }

  .empty-container h3 {
    color: #f2f2f7;
  }

  .timeline-event {
    background: rgba(44, 44, 46, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .event-action {
    color: #f2f2f7;
  }

  .content-text {
    color: #8e8e93;
  }

  .changes-list {
    background: rgba(58, 58, 60, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .changes-header {
    color: #f2f2f7;
  }

  .field-name {
    color: #f2f2f7;
  }
}
</style>
