<script setup lang="ts">
import { ref, watch } from "vue";
import type { ContractItem, ContractFileItem } from "../../types/contract";
import { contractTermMap } from "../../utils/const";
import { getContractFiles, previewContractFile } from "../../services/contract";
import { useToast } from "primevue/usetoast";

const props = defineProps<{
  visible: boolean;
  contract: ContractItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const dialogVisible = ref(false);
const toast = useToast();

// 文件相关状态
const contractFiles = ref<ContractFileItem[]>([]);
const filesLoading = ref(false);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.contract) {
      loadContractFiles();
    }
  }
);

// 监听内部状态变化
watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 格式化金额
const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return "--";
  return `¥${amount.toLocaleString()}`;
};

// 格式化是否字段
const formatYesNo = (value: string | null | undefined) => {
  if (value === "Y") return "是";
  if (value === "N") return "否";
  return "--";
};

// 加载合同文件列表
const loadContractFiles = async () => {
  if (!props.contract?.id) return;

  filesLoading.value = true;
  try {
    const response = await getContractFiles(props.contract.id);
    if (response.code === 200) {
      contractFiles.value = response.data;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取合同文件失败",
        life: 3000,
      });
    }
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取合同文件失败",
      life: 3000,
    });
  } finally {
    filesLoading.value = false;
  }
};

// 预览合同文件
const previewFile = async (fileId: number) => {
  if (!props.contract?.id) return;

  try {
    const result = await previewContractFile(props.contract.id, fileId);

    // 创建预览链接并在新窗口打开
    const url = window.URL.createObjectURL(result.blob);
    window.open(url, "_blank");

    // 延迟释放URL对象
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
    }, 1000);
  } catch (error: any) {
    console.error("预览文件失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "预览文件失败",
      life: 3000,
    });
  }
};
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    modal
    :header="`合同详情 - ${contract?.contract_title || ''}`"
    :style="{ width: '80rem' }"
    :closable="true"
    :dismissableMask="false"
    @hide="closeDialog"
    maximizable
  >
    <div v-if="contract" class="contract-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          基本信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>合同编码</label>
              <span class="detail-value">{{ contract.contract_num }}</span>
            </div>
            <div class="detail-item">
              <label>合同标题</label>
              <span class="detail-value">{{ contract.contract_title }}</span>
            </div>
            <div class="detail-item">
              <label>客户编码</label>
              <span class="detail-value">{{ contract.main_customer_num }}</span>
            </div>
            <div class="detail-item">
              <label>业务类型</label>
              <span class="detail-value">{{ contract.business || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>合同类型</label>
              <span class="detail-value">{{
                contract.contract_type || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>销售</label>
              <span class="detail-value">{{ contract.sale_name || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>状态</label>
              <span class="detail-value">{{ contract.state || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>签约主体</label>
              <span class="detail-value">{{
                contract.sign_contract_entity || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 合同条款 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-bars"></i>
          合同条款
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>合同期限</label>
              <span class="detail-value">{{
                contractTermMap[contract.contract_term] || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同开始日期</label>
              <span class="detail-value">{{
                contract.contract_start_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同结束日期</label>
              <span class="detail-value">{{
                contract.contract_end_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同月度金额</label>
              <span class="detail-value">{{
                formatCurrency(contract.contract_month_amount)
              }}</span>
            </div>
            <div class="detail-item">
              <label>是否自动延期</label>
              <span class="detail-value">{{
                formatYesNo(contract.is_auto_delay)
              }}</span>
            </div>
            <div class="detail-item">
              <label>自动延期次数</label>
              <span class="detail-value">{{
                contract.auto_delay_num || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 编码信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-code"></i>
          编码信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>别名客户编码</label>
              <span class="detail-value">{{
                contract.alias_customer_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>对方编码</label>
              <span class="detail-value">{{
                contract.other_party_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>框架合同编码</label>
              <span class="detail-value">{{
                contract.frame_contract_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同法务编码</label>
              <span class="detail-value">{{
                contract.contract_legal_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>框架法务编码</label>
              <span class="detail-value">{{
                contract.frame_legal_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同OA编码</label>
              <span class="detail-value">{{
                contract.contract_oa_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>框架OA编码</label>
              <span class="detail-value">{{
                contract.frame_oa_num || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 联系信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-users"></i>
          客户信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>客户联系人姓名</label>
              <span class="detail-value">{{ contract.contact_person || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>客户联系电话</label>
              <span class="detail-value">{{ contract.contact_telephone || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>客户联系邮箱</label>
              <span class="detail-value">{{ contract.contact_email || "--" }}</span>
            </div>
            <div class="detail-item" style="grid-column: span 3">
              <label>客户联系地址</label>
              <div class="detail-text">
                {{ contract.contact_address || "--" }}
              </div>
            </div>
            <div class="detail-item">
              <label>审批人邮箱</label>
              <span class="detail-value">{{ contract.applicant_name || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>银行账号</label>
              <span class="detail-value">{{ contract.bank_account_number || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>银行户名</label>
              <span class="detail-value">{{ contract.bank_account_name || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>开户行名称</label>
              <span class="detail-value">{{ contract.bank_name || "--" }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 补充信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-file-edit"></i>
          补充信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-1 gap-3">
            <div class="detail-item">
              <label>合同摘要</label>
              <div class="detail-text">
                {{ contract.contract_summary || "--" }}
              </div>
            </div>
            <div class="detail-item">
              <label>补充合同方式</label>
              <span class="detail-value">{{
                contract.append_contract_way || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>补充合同说明</label>
              <div class="detail-text">
                {{ contract.append_contract_explain || "--" }}
              </div>
            </div>
            <div class="detail-item">
              <label>备注</label>
              <div class="detail-text">{{ contract.remark || "--" }}</div>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 合同文件 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-file"></i>
          合同文件
        </h3>
        <div v-if="filesLoading" class="flex justify-center items-center py-8">
          <i class="pi pi-spin pi-spinner text-4xl text-blue-500"></i>
        </div>
        <div
          v-else-if="contractFiles.length === 0"
          class="text-center py-8 text-gray-500"
        >
          <i class="pi pi-inbox text-4xl mb-4 block"></i>
          <p>暂无合同文件</p>
        </div>
        <div v-else class="space-y-3">
          <div
            v-for="file in contractFiles"
            :key="file.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors"
          >
            <div class="flex items-center gap-3">
              <i class="pi pi-file-pdf text-red-500 text-xl"></i>
              <div>
                <div class="font-medium text-gray-900">
                  {{ file.file_name }}
                </div>
                <div class="text-sm text-gray-500">
                  状态:
                  <Tag
                    :value="file.status === 'signed' ? '已归档' : '未归档'"
                    :severity="file.status === 'signed' ? 'success' : 'warn'"
                    size="small"
                  />
                </div>
              </div>
            </div>
            <div class="flex gap-2">
              <Button
                icon="pi pi-file"
                @click="previewFile(file.id)"
                outlined
                rounded
                severity="info"
                v-tooltip.top="'预览文件'"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          label="关闭"
          icon="pi pi-times"
          @click="closeDialog"
          class="apple-button"
          severity="secondary"
          outlined
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.contract-detail-content {
  max-height: 70vh;
  overflow-y: auto;
  background: #fbfbfd;
}

.detail-section {
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--p-primary-color);
}

.section-title i {
  color: var(--p-primary-color);
  font-size: 1rem;
}

.detail-section .p-fluid {
  padding: 0.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #6e6e73;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.detail-text {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 3rem;
  white-space: pre-wrap;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.apple-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.apple-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 滚动条样式 */
.contract-detail-content::-webkit-scrollbar {
  width: 6px;
}

.contract-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.contract-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.contract-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
