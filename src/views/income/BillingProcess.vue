<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getOrderDetail,
  changeBillingStatus,
} from "../../services/income/order";
import type { OrderItem } from "../../types/order";
import { useRouter } from "vue-router";
import { isoFormatDate } from "../../utils/common";
import { getAccountSeqSimpleList } from "../../services/invoice";

const props = defineProps<{
  orderId: number;
  confirm: any;
  dialogKey: string;
}>();

const emit = defineEmits<{
  statusChanged: [];
}>();

const router = useRouter();
const toast = useToast();
const loading = ref(false);
const orderDetail = ref<OrderItem | null>(null);
const activeStep = ref("I");
const selectedBranch = ref<"billing" | "termination" | "modification">(
  "billing"
);

// 表单数据
const formData = ref({
  accountSeq: "",
  realityBillStartDate: null as Date | null,
  orderStartYear: null as Date | null,
  orderRemark: "",
  finishedRemark: "",
  actualBillingEndDate: null as Date | null, // 实际计费终日
});

// 定义状态类型
type BillingStatus = "未完工" | "新装计费确认" | "新装账务确认" | "计费中";

type TerminationBillingStatus = "拆机计费确认" | "拆机账务确认" | "计费终止";

type ModificationBillingStatus =
  | "变更拆机计费确认"
  | "变更拆机账务确认"
  | "变更计费终止";

type BillingStepValue = "I" | "II" | "III" | "IV";
type TerminationStepValue = "I" | "II" | "III";
type ModificationStepValue = "I" | "II" | "III";

type StepValue =
  | BillingStepValue
  | TerminationStepValue
  | ModificationStepValue;

// 计费流程状态
const BILLING_STATUSES = [
  { value: "I", label: "未完工" },
  { value: "II", label: "新装计费确认" },
  { value: "III", label: "新装账务确认" },
  { value: "IV", label: "计费中" },
] as const;

// 计费终止流程状态
const TERMINATION_BILLING_STATUSES = [
  { value: "I", label: "拆机计费确认" },
  { value: "II", label: "拆机账务确认" },
  { value: "III", label: "计费终止" },
] as const;

// 变更计费终止流程状态
const MODIFICATION_BILLING_STATUSES = [
  { value: "I", label: "变更拆机计费确认" },
  { value: "II", label: "变更拆机账务确认" },
  { value: "III", label: "变更计费终止" },
] as const;

// 当前流程状态列表
const currentStatuses = computed(() => {
  switch (selectedBranch.value) {
    case "billing":
      return BILLING_STATUSES;
    case "termination":
      return TERMINATION_BILLING_STATUSES;
    case "modification":
      return MODIFICATION_BILLING_STATUSES;
  }
});

// 状态值映射
const billingStatusValueMap: Record<BillingStatus, BillingStepValue> = {
  未完工: "I",
  新装计费确认: "II",
  新装账务确认: "III",
  计费中: "IV",
};

const terminationBillingStatusValueMap: Record<
  TerminationBillingStatus,
  TerminationStepValue
> = {
  拆机计费确认: "I",
  拆机账务确认: "II",
  计费终止: "III",
};

const modificationBillingStatusValueMap: Record<
  ModificationBillingStatus,
  ModificationStepValue
> = {
  变更拆机计费确认: "I",
  变更拆机账务确认: "II",
  变更计费终止: "III",
};

const billingStatusLabelMap: Record<BillingStepValue, BillingStatus> = {
  I: "未完工",
  II: "新装计费确认",
  III: "新装账务确认",
  IV: "计费中",
};

const terminationBillingStatusLabelMap: Record<
  TerminationStepValue,
  TerminationBillingStatus
> = {
  I: "拆机计费确认",
  II: "拆机账务确认",
  III: "计费终止",
};

const modificationBillingStatusLabelMap: Record<
  ModificationStepValue,
  ModificationBillingStatus
> = {
  I: "变更拆机计费确认",
  II: "变更拆机账务确认",
  III: "变更计费终止",
};

// 获取当前状态的标签
const getStatusLabel = (stepValue: StepValue): string => {
  if (selectedBranch.value === "termination") {
    return terminationBillingStatusLabelMap[stepValue as TerminationStepValue];
  } else if (selectedBranch.value === "modification") {
    return modificationBillingStatusLabelMap[
      stepValue as ModificationStepValue
    ];
  } else {
    return billingStatusLabelMap[stepValue as BillingStepValue];
  }
};

// 加载分账序号选项
const accountSeqOptions = ref<{ label: string; value: string }[]>([]);
const loadAccounSeqOptions = async (customer_num: string) => {
  try {
    const response = await getAccountSeqSimpleList(customer_num);
    accountSeqOptions.value = response.data.map((item) => ({
      label: `${item.seq_name}(${item.account_seq})`,
      value: item.account_seq,
    }));
    // 如果当前没有选择分账序号且有可用选项，自动设置第一个为默认值
    if (!formData.value.accountSeq && accountSeqOptions.value.length > 0) {
      formData.value.accountSeq = accountSeqOptions.value[0].value;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载分账序号选项失败",
      life: 3000,
    });
  }
};

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    loading.value = true;
    const response = await getOrderDetail(props.orderId);
    orderDetail.value = response.data;

    // 确定当前流程和状态
    const status = orderDetail.value.bill_status as string;

    // 确定当前所处的流程分支
    if (status in billingStatusValueMap) {
      selectedBranch.value = "billing";
      activeStep.value = billingStatusValueMap[status as BillingStatus];
    } else if (status in terminationBillingStatusValueMap) {
      selectedBranch.value = "termination";
      activeStep.value =
        terminationBillingStatusValueMap[status as TerminationBillingStatus];
    } else if (status in modificationBillingStatusValueMap) {
      selectedBranch.value = "modification";
      activeStep.value =
        modificationBillingStatusValueMap[status as ModificationBillingStatus];
    } else {
      // 默认为计费流程的第一步
      selectedBranch.value = "billing";
      activeStep.value = "I";
    }

    // 根据合同编号加载分账序号选项
    if (orderDetail.value.contract_num) {
      await loadAccounSeqOptions(orderDetail.value.customer_num);
    }

    // 初始化表单数据
    initializeFormData();
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载订单详情失败",
      life: 3000,
    });
    router.push("/order-management/orders-info");
  } finally {
    loading.value = false;
  }
};

// 初始化表单数据
const initializeFormData = () => {
  if (!orderDetail.value) return;
  // 初始化实际计费始日
  if (orderDetail.value.reality_bill_start_date) {
    formData.value.realityBillStartDate = new Date(
      orderDetail.value.reality_bill_start_date
    );
  }
  // 初始化订单起始年
  if (orderDetail.value.order_start_year) {
    formData.value.orderStartYear = new Date(
      orderDetail.value.order_start_year
    );
  }
  // 初始化订单备注
  if (orderDetail.value.order_remark) {
    formData.value.orderRemark = orderDetail.value.order_remark;
  }
  // 初始化完工备注
  if (orderDetail.value.finished_remark) {
    formData.value.finishedRemark = orderDetail.value.finished_remark;
  }
  // 初始化实际计费终日
  if (orderDetail.value.reality_bill_end_date) {
    formData.value.actualBillingEndDate = new Date(
      orderDetail.value.reality_bill_end_date
    );
  }
};

// 切换到计费终止流程
const goToBillingTerminationProcess = (
  type: "termination" | "modification"
) => {
  if (!orderDetail.value) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "订单信息未加载",
      life: 3000,
    });
    return;
  }

  // 检查服务状态是否允许切换到计费终止流程
  const serviceStatus = orderDetail.value.service_status;
  let allowedStatuses: string[] = [];

  if (type === "termination") {
    allowedStatuses = ["拆机实施", "拆机维护信息复核", "服务终止"];
  } else if (type === "modification") {
    allowedStatuses = ["变更拆机实施", "变更拆机维护信息复核", "变更服务终止"];
  }

  if (!allowedStatuses.includes(serviceStatus)) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: `当前服务状态（${serviceStatus}）不支持${
        type === "termination" ? "计费终止" : "变更计费终止"
      }流程`,
      life: 3000,
    });
    return;
  }

  if (orderDetail.value.bill_status !== "计费中") {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "只有在计费中状态才可以选择计费终止流程",
      life: 3000,
    });
    return;
  }

  props.confirm.require({
    group: props.dialogKey,
    header: `确认进入${
      type === "termination" ? "计费终止" : "变更计费终止"
    }流程`,
    icon: "pi pi-exclamation-triangle",
    message: `是否确认进入「${
      type === "termination" ? "计费终止" : "变更计费终止"
    }」流程？此操作不可逆。`,
    rejectProps: {
      label: "取消",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "确认",
      severity: "success",
    },
    accept: async () => {
      try {
        // 首先更新计费状态到对应流程的第一步
        const orderId = Number(props.orderId);
        if (isNaN(orderId)) {
          throw new Error("Invalid order ID");
        }

        const newStatus =
          type === "termination" ? "拆机计费确认" : "变更拆机计费确认";
        await changeBillingStatus(orderId, { bill_status: newStatus as any });

        // 切换当前tab的流程分支
        selectedBranch.value = type;
        activeStep.value = "I";

        toast.add({
          severity: "success",
          summary: "成功",
          detail: `已切换到${
            type === "termination" ? "计费终止" : "变更计费终止"
          }流程`,
          life: 3000,
        });

        await loadOrderDetail();

        // 通知父组件状态已变更，需要重新加载订单列表
        emit('statusChanged');
      } catch (error) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "切换流程失败",
          life: 3000,
        });
      }
    },
    reject: () => {},
  });
};

// 检查按钮是否应该禁用
const isButtonDisabled = (
  step: StepValue,
  direction: "next" | "back"
): boolean => {
  if (!orderDetail.value) return true;

  let currentStatus: StepValue;
  if (selectedBranch.value === "billing") {
    if (
      (orderDetail.value.bill_status as BillingStatus) in billingStatusValueMap
    ) {
      currentStatus =
        billingStatusValueMap[orderDetail.value.bill_status as BillingStatus];
    } else {
      return true;
    }
  } else if (selectedBranch.value === "termination") {
    if (
      (orderDetail.value.bill_status as TerminationBillingStatus) in
      terminationBillingStatusValueMap
    ) {
      currentStatus =
        terminationBillingStatusValueMap[
          orderDetail.value.bill_status as TerminationBillingStatus
        ];
    } else {
      return true;
    }
  } else if (selectedBranch.value === "modification") {
    if (
      (orderDetail.value.bill_status as ModificationBillingStatus) in
      modificationBillingStatusValueMap
    ) {
      currentStatus =
        modificationBillingStatusValueMap[
          orderDetail.value.bill_status as ModificationBillingStatus
        ];
    } else {
      return true;
    }
  } else {
    return true;
  }

  // 如果当前是最终状态，禁用下一步按钮
  if (
    (selectedBranch.value === "billing" && currentStatus === "IV") ||
    (selectedBranch.value === "termination" && currentStatus === "III") ||
    (selectedBranch.value === "modification" && currentStatus === "III")
  ) {
    if (direction === "next") return true;
  }

  // 计费终止和变更计费终止不可进行回退操作
  if (
    (selectedBranch.value === "termination" &&
      currentStatus === "III" &&
      direction === "back") ||
    (selectedBranch.value === "modification" &&
      currentStatus === "III" &&
      direction === "back")
  ) {
    return true;
  }

  // 如果不是当前步骤，禁用按钮
  if (currentStatus !== step) return true;

  if (direction === "next") {
    // 验证必填字段
    if (selectedBranch.value === "billing") {
      // 计费流程
      switch (step) {
        case "II": // 新装计费确认
          if (!formData.value.realityBillStartDate) return true;
          break;
        case "III": // 新装账务确认
          if (!formData.value.accountSeq || !formData.value.orderStartYear)
            return true;
          break;
      }
    } else if (
      selectedBranch.value === "termination" ||
      selectedBranch.value === "modification"
    ) {
      // 计费终止流程和变更计费终止流程
      switch (step) {
        case "I": // 拆机计费确认/变更拆机计费确认
          if (
            !formData.value.actualBillingEndDate ||
            !formData.value.orderRemark ||
            !formData.value.finishedRemark
          )
            return true;
          break;
        case "II": // 拆机账务确认/变更拆机账务确认
          if (!formData.value.orderRemark || !formData.value.finishedRemark)
            return true;
          break;
      }
    }
  }

  return false;
};

// 处理状态变更
const handleStatusChange = async (
  newStatus: StepValue,
  direction: "next" | "back"
) => {
  if (!orderDetail.value) return;

  props.confirm.require({
    group: props.dialogKey,
    header: "确认变更状态",
    icon: "pi pi-exclamation-triangle",
    message: `是否确认${
      direction === "next" ? "进入" : "返回"
    }「${getStatusLabel(newStatus)}」状态？`,
    rejectProps: {
      label: "取消",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "确认",
      severity: "success",
    },
    accept: async () => {
      try {
        const orderId = Number(props.orderId);
        if (isNaN(orderId)) {
          throw new Error("Invalid order ID");
        }

        // 构建请求数据
        const postData: {
          bill_status: string;
          account_seq?: string;
          reality_bill_start_date?: string;
          order_start_year?: string;
          order_remark?: string;
          finished_remark?: string;
          reality_bill_end_date?: string;
        } = {
          bill_status: getStatusLabel(newStatus),
        };
        // 根据不同状态添加不同字段
        if (selectedBranch.value === "billing") {
          // 计费流程
          switch (newStatus) {
            case "I": // 返回计费未完工
              break;
            case "II": // 从计费未完工到新装计费确认
              // 进入新装计费确认阶段不需要填写任何信息
              break;
            case "III": // 从新装计费确认到新装账务确认
              if (!formData.value.realityBillStartDate) {
                toast.add({
                  severity: "error",
                  summary: "错误",
                  detail: "请填写必填字段：实际计费始日",
                  life: 3000,
                });
                return;
              }
              postData.reality_bill_start_date = isoFormatDate(
                formData.value.realityBillStartDate
              );
              if (formData.value.orderRemark) {
                postData.order_remark = formData.value.orderRemark;
              }
              if (formData.value.finishedRemark) {
                postData.finished_remark = formData.value.finishedRemark;
              }
              break;
            case "IV": // 从新装账务确认到计费中
              if (
                !formData.value.orderStartYear ||
                !formData.value.accountSeq
              ) {
                toast.add({
                  severity: "error",
                  summary: "错误",
                  detail: "请填写必填字段：分账序号和订单起始年",
                  life: 3000,
                });
                return;
              }
              postData.account_seq = formData.value.accountSeq;
              postData.order_start_year = formData.value.orderStartYear
                .getFullYear()
                .toString();
              if (formData.value.orderRemark) {
                postData.order_remark = formData.value.orderRemark;
              }
              if (formData.value.finishedRemark) {
                postData.finished_remark = formData.value.finishedRemark;
              }
              break;
          }
        } else if (selectedBranch.value === "termination") {
          // 计费终止流程
          switch (newStatus) {
            case "I": // 拆机计费确认
              // 拆机计费确认不需要额外字段
              break;
            case "II": // 拆机计费确认到拆机账务确认
              if (
                !formData.value.actualBillingEndDate ||
                !formData.value.orderRemark ||
                !formData.value.finishedRemark
              ) {
                toast.add({
                  severity: "error",
                  summary: "错误",
                  detail: "请填写实际计费终日、订单备注和完工备注",
                  life: 3000,
                });
                return;
              }
              postData.reality_bill_end_date = isoFormatDate(
                formData.value.actualBillingEndDate
              );
              postData.order_remark = formData.value.orderRemark;
              postData.finished_remark = formData.value.finishedRemark;
              break;
            case "III": // 拆机账务确认到计费终止
              if (
                !formData.value.orderRemark ||
                !formData.value.finishedRemark
              ) {
                toast.add({
                  severity: "error",
                  summary: "错误",
                  detail: "请填写订单备注和完工备注",
                  life: 3000,
                });
                return;
              }
              postData.order_remark = formData.value.orderRemark;
              postData.finished_remark = formData.value.finishedRemark;
              break;
          }
        } else if (selectedBranch.value === "modification") {
          // 变更计费终止流程
          switch (newStatus) {
            case "I": // 变更拆机计费确认
              // 变更拆机计费确认不需要额外字段
              break;
            case "II": // 变更拆机计费确认到变更拆机账务确认
              if (
                !formData.value.actualBillingEndDate ||
                !formData.value.orderRemark ||
                !formData.value.finishedRemark
              ) {
                toast.add({
                  severity: "error",
                  summary: "错误",
                  detail: "请填写实际计费终日、订单备注和完工备注",
                  life: 3000,
                });
                return;
              }
              postData.reality_bill_end_date = isoFormatDate(
                formData.value.actualBillingEndDate
              );
              postData.order_remark = formData.value.orderRemark;
              postData.finished_remark = formData.value.finishedRemark;
              break;
            case "III": // 变更拆机账务确认到变更计费终止
              if (
                !formData.value.orderRemark ||
                !formData.value.finishedRemark
              ) {
                toast.add({
                  severity: "error",
                  summary: "错误",
                  detail: "请填写订单备注和完工备注",
                  life: 3000,
                });
                return;
              }
              postData.order_remark = formData.value.orderRemark;
              postData.finished_remark = formData.value.finishedRemark;
              break;
          }
        }

        await changeBillingStatus(orderId, postData);

        toast.add({
          severity: "success",
          summary: "成功",
          detail: "状态更新成功",
          life: 3000,
        });
        activeStep.value = newStatus;
        await loadOrderDetail();

        // 通知父组件状态已变更，需要重新加载订单列表
        emit('statusChanged');
      } catch (error) {
        console.error("Status change failed:", error);
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "状态更新失败",
          life: 3000,
        });
      }
    },
    reject: () => {
      console.log("Status change rejected by user");
    },
  });
};

onMounted(() => {
  loadOrderDetail();
});
</script>

<template>
  <div class="billing-process">
    <div class="card-header">
      <Message severity="info">计费审核</Message>
      <div class="order-info" v-if="orderDetail">
        <span>订单号：{{ orderDetail.total_num }}</span>
        <span class="ml-4">当前状态：{{ orderDetail.bill_status }}</span>
      </div>
    </div>

    <Stepper v-model:value="activeStep" class="mt-4">
      <StepList>
        <Step
          v-for="status in currentStatuses"
          :key="status.value"
          :value="status.value"
        >
          {{ status.label }}
        </Step>
      </StepList>
      <StepPanels class="step-table">
        <!-- 计费流程 -->
        <template v-if="selectedBranch === 'billing'">
          <!-- 计费未完工 -->
          <StepPanel value="I">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：计费未完工</p>
              <div class="flex justify-end mt-4">
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('I', 'next')"
                  @click="handleStatusChange('II', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 新装计费确认 -->
          <StepPanel value="II">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：新装计费确认</p>
              <div class="grid grid-cols-1 gap-3">
                <div class="field">
                  <label class="required">实际计费始日</label>
                  <DatePicker
                    v-model="formData.realityBillStartDate"
                    dateFormat="yy-mm-dd"
                    :showIcon="true"
                    class="w-100"
                  />
                </div>
                <div class="field">
                  <label>订单备注</label>
                  <Textarea
                    v-model="formData.orderRemark"
                    rows="3"
                    autoResize
                  />
                </div>
                <div class="field">
                  <label>完工备注</label>
                  <Textarea
                    v-model="formData.finishedRemark"
                    rows="3"
                    autoResize
                  />
                </div>
              </div>
              <div class="flex justify-between mt-4">
                <Button
                  label="上一步"
                  icon="pi pi-arrow-left"
                  severity="secondary"
                  :disabled="isButtonDisabled('II', 'back')"
                  @click="handleStatusChange('I', 'back')"
                />
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('II', 'next')"
                  @click="handleStatusChange('III', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 新装账务确认 -->
          <StepPanel value="III">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：新装账务确认</p>
              <Fluid>
                <div class="grid grid-cols-2 gap-3">
                  <div class="field">
                    <label class="required">关联分账序号</label>
                    <Select
                      v-model="formData.accountSeq"
                      :options="accountSeqOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择分账序号"
                    />
                  </div>
                  <div class="field">
                    <label class="required">订单起始年</label>
                    <DatePicker
                      v-model="formData.orderStartYear"
                      view="year"
                      dateFormat="yy"
                      :showIcon="true"
                      disabled
                    />
                  </div>
                </div>
                <div class="grid grid-cols-1 gap-3">
                  <div class="field">
                    <label>订单备注</label>
                    <Textarea
                      v-model="formData.orderRemark"
                      rows="3"
                      autoResize
                    />
                  </div>
                  <div class="field">
                    <label>完工备注</label>
                    <Textarea
                      v-model="formData.finishedRemark"
                      rows="3"
                      autoResize
                    />
                  </div>
                </div>
              </Fluid>
              <div class="flex justify-between mt-4">
                <Button
                  label="上一步"
                  icon="pi pi-arrow-left"
                  severity="secondary"
                  :disabled="isButtonDisabled('III', 'back')"
                  @click="handleStatusChange('II', 'back')"
                />
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('III', 'next')"
                  @click="handleStatusChange('IV', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 计费中 -->
          <StepPanel value="IV">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：计费中</p>
              <div class="branch-selection">
                <p class="text-sm text-gray-600 mb-2">请选择后续流程：</p>
                <div class="flex gap-3 mt-3">
                  <Button
                    label="计费终止"
                    icon="pi pi-times-circle"
                    severity="danger"
                    :disabled="
                      !orderDetail ||
                      !['拆机实施', '拆机维护信息复核', '服务终止'].includes(
                        orderDetail.service_status
                      )
                    "
                    @click="goToBillingTerminationProcess('termination')"
                    v-tooltip.top="
                      orderDetail &&
                      ['拆机实施', '拆机维护信息复核', '服务终止'].includes(
                        orderDetail.service_status
                      )
                        ? '进入计费终止流程'
                        : '需要服务状态为拆机实施、拆机维护信息复核或服务终止时才可点击'
                    "
                  />
                  <Button
                    label="变更计费终止"
                    icon="pi pi-sync"
                    severity="warning"
                    :disabled="
                      !orderDetail ||
                      ![
                        '变更拆机实施',
                        '变更拆机维护信息复核',
                        '变更服务终止',
                      ].includes(orderDetail.service_status)
                    "
                    @click="goToBillingTerminationProcess('modification')"
                    v-tooltip.top="
                      orderDetail &&
                      [
                        '变更拆机实施',
                        '变更拆机维护信息复核',
                        '变更服务终止',
                      ].includes(orderDetail.service_status)
                        ? '进入变更计费终止流程'
                        : '需要服务状态为变更拆机实施、变更拆机维护信息复核或变更服务终止时才可点击'
                    "
                  />
                </div>
              </div>
              <Message severity="info" class="w-full mt-4">
                <span
                  >订单已进入计费中状态，请根据当前服务状态选择相应的计费终止流程</span
                >
              </Message>
            </div>
          </StepPanel>
        </template>

        <!-- 计费终止流程 -->
        <template v-else-if="selectedBranch === 'termination'">
          <!-- 拆机计费确认 -->
          <StepPanel value="I">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：拆机计费确认</p>
              <div class="grid grid-cols-1 gap-4">
                <div class="field">
                  <label
                    >实际计费终日 <span class="text-red-500">*</span></label
                  >
                  <DatePicker
                    v-model="formData.actualBillingEndDate"
                    :showIcon="true"
                    placeholder="选择实际计费终日"
                  />
                </div>
                <div class="field">
                  <label>订单备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.orderRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写订单备注"
                  />
                </div>
                <div class="field">
                  <label>完工备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.finishedRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写完工备注"
                  />
                </div>
              </div>
              <div class="flex justify-end mt-4">
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('I', 'next')"
                  @click="handleStatusChange('II', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 拆机账务确认 -->
          <StepPanel value="II">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：拆机账务确认</p>
              <div class="grid grid-cols-1 gap-4">
                <div class="field">
                  <label>订单备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.orderRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写订单备注"
                  />
                </div>
                <div class="field">
                  <label>完工备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.finishedRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写完工备注"
                  />
                </div>
              </div>
              <div class="flex justify-between mt-4">
                <Button
                  label="上一步"
                  icon="pi pi-arrow-left"
                  severity="secondary"
                  :disabled="isButtonDisabled('II', 'back')"
                  @click="handleStatusChange('I', 'back')"
                />
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('II', 'next')"
                  @click="handleStatusChange('III', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 计费终止 -->
          <StepPanel value="III">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：计费终止</p>
              <Message severity="success" class="w-full mt-4">
                <span>订单已完成计费终止流程，不可进行回退操作</span>
              </Message>
            </div>
          </StepPanel>
        </template>

        <!-- 变更计费终止流程 -->
        <template v-else-if="selectedBranch === 'modification'">
          <!-- 变更拆机计费确认 -->
          <StepPanel value="I">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：变更拆机计费确认</p>
              <div class="grid grid-cols-1 gap-4">
                <div class="field">
                  <label
                    >实际计费终日 <span class="text-red-500">*</span></label
                  >
                  <DatePicker
                    v-model="formData.actualBillingEndDate"
                    :showIcon="true"
                    placeholder="选择实际计费终日"
                  />
                </div>
                <div class="field">
                  <label>订单备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.orderRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写订单备注"
                  />
                </div>
                <div class="field">
                  <label>完工备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.finishedRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写完工备注"
                  />
                </div>
              </div>
              <div class="flex justify-end mt-4">
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('I', 'next')"
                  @click="handleStatusChange('II', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 变更拆机账务确认 -->
          <StepPanel value="II">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：变更拆机账务确认</p>
              <div class="grid grid-cols-1 gap-4">
                <div class="field">
                  <label>订单备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.orderRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写订单备注"
                  />
                </div>
                <div class="field">
                  <label>完工备注 <span class="text-red-500">*</span></label>
                  <Textarea
                    v-model="formData.finishedRemark"
                    rows="3"
                    autoResize
                    placeholder="请填写完工备注"
                  />
                </div>
              </div>
              <div class="flex justify-between mt-4">
                <Button
                  label="上一步"
                  icon="pi pi-arrow-left"
                  severity="secondary"
                  :disabled="isButtonDisabled('II', 'back')"
                  @click="handleStatusChange('I', 'back')"
                />
                <Button
                  label="下一步"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  :disabled="isButtonDisabled('II', 'next')"
                  @click="handleStatusChange('III', 'next')"
                />
              </div>
            </div>
          </StepPanel>

          <!-- 变更计费终止 -->
          <StepPanel value="III">
            <div class="step-content">
              <p class="text-lg mb-4">当前状态：变更计费终止</p>
              <Message severity="success" class="w-full mt-4">
                <span>订单已完成变更计费终止流程，不可进行回退操作</span>
              </Message>
            </div>
          </StepPanel>
        </template>
      </StepPanels>
      <ConfirmDialog :group="props.dialogKey" />
    </Stepper>
  </div>
</template>

<style scoped>
.billing-process {
  padding: 1rem;
  height: calc(100vh - 20rem);
  max-width: 100rem;
  margin: 0 auto;
}

.card-header {
  margin-bottom: 2rem;
}

.order-info {
  margin-top: 1rem;
  color: #666;
}

.step-content {
  padding: 1rem;
  background-color: var(--surface-card);
  border-radius: 8px;
}

.field {
  margin-bottom: 1.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--p-text-color);
}

.field label.required::after {
  content: " *";
  color: var(--p-red-500);
}

.branch-selection {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  background-color: #f9f9f9;
}

:deep(.p-stepper-header) {
  border-bottom: 1px solid var(--surface-border);
  margin-bottom: 1.5rem;
}

:deep(.p-stepper-step-icon) {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--surface-ground);
  color: var(--text-color);
  border: 2px solid var(--surface-border);
  transition: all 0.2s;
}

:deep(.p-stepper-step.p-highlight .p-stepper-step-icon) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
  border-color: var(--primary-color);
}

:deep(.p-stepper-step.p-disabled .p-stepper-step-icon) {
  background-color: var(--surface-ground);
  color: var(--text-color-secondary);
  border-color: var(--surface-border);
  cursor: not-allowed;
}

:deep(.p-calendar),
:deep(.p-dropdown),
:deep(.p-textarea) {
  width: 100%;
}

.step-table {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 33rem);
  overflow-y: auto;
}
</style>
